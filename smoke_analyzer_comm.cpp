#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#ifdef _WIN32
    #ifndef WIN32_LEAN_AND_MEAN
    #define WIN32_LEAN_AND_MEAN
    #endif
    #include <windows.h>
    #include <io.h>
    #include <fcntl.h>
    #define O_RDWR _O_RDWR

    // Windows串口读写函数
    int win_serial_read(int fd, void* buffer, int length) {
        HANDLE hSerial = (HANDLE)(intptr_t)fd;
        DWORD bytesRead;
        if (ReadFile(hSerial, buffer, length, &bytesRead, NULL)) {
            return (int)bytesRead;
        }
        return -1;
    }

    int win_serial_write(int fd, const void* buffer, int length) {
        HANDLE hSerial = (HANDLE)(intptr_t)fd;
        DWORD bytesWritten;
        if (WriteFile(hSerial, buffer, length, &bytesWritten, NULL)) {
            return (int)bytesWritten;
        }
        return -1;
    }

    void win_serial_close(int fd) {
        HANDLE hSerial = (HANDLE)(intptr_t)fd;
        CloseHandle(hSerial);
    }

    #define close win_serial_close
    #define read win_serial_read
    #define write win_serial_write
    #define open _open
#else
    #include <unistd.h>
    #include <fcntl.h>
    #include <termios.h>
#endif
#include "csvfile.h"
#include <mutex>
#include "smoke_analyzer_comm.h"
#include <regex>
#include <vector>
#include <chrono>
#include <thread>
#include <cstdarg>


// 定义Modbus功能码
#define READ_COILS 0x01
#define READ_DISCRETE_INPUTS 0x02
#define READ_HOLDING_REGISTERS 0x03
#define READ_INPUT_REGISTERS 0x04
#define WRITE_SINGLE_COIL 0x05
#define WRITE_SINGLE_REGISTER 0x06

std::mutex rwMutex;

// 全局串口访问互斥锁，解决COM3端口共享问题
std::mutex g_serial_port_mutex;

// 调试输出函数，支持中文
void debug_printf(const char* format, ...) {
#ifdef DEBUG
    va_list args;
    va_start(args, format);

#ifdef _WIN32
    // Windows下确保使用UTF-8编码输出
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);

    // 转换为宽字符
    int wlen = MultiByteToWideChar(CP_UTF8, 0, buffer, -1, NULL, 0);
    if (wlen > 0) {
        wchar_t* wbuffer = new wchar_t[wlen];
        MultiByteToWideChar(CP_UTF8, 0, buffer, -1, wbuffer, wlen);

        // 输出到控制台
        WriteConsoleW(GetStdHandle(STD_OUTPUT_HANDLE), wbuffer, wcslen(wbuffer), NULL, NULL);

        delete[] wbuffer;
    }
#else
    // Linux/Unix下直接输出
    vprintf(format, args);
#endif

    va_end(args);
#else
    // Release模式下不输出任何内容
    (void)format; // 避免未使用参数警告
#endif
}

// 错误输出函数，支持中文
void debug_perror(const char* msg) {
#ifdef DEBUG
#ifdef _WIN32
    // Windows下获取错误信息并使用debug_printf输出
    char buffer[1024];
    strerror_s(buffer, sizeof(buffer), errno);
    debug_printf("%s: %s\n", msg, buffer);
#else
    // Linux/Unix下直接调用perror
    perror(msg);
#endif
#else
    // Release模式下不输出任何内容
    (void)msg; // 避免未使用参数警告
#endif
}

// float f = hex_to_float(0x42B10000); // 88.5
// printf("Hex 0x42B10000 转换为浮点数: %f\n", f);
float hex_to_float(uint32_t hex_value) {
    float result;
    memcpy(&result, &hex_value, sizeof(float));
    return result;
}

//打印调试信息
void print_result(unsigned int *registers, int count, bool is32bit) {
// 打印结果
#ifdef DEBUG
    debug_printf("成功读取 %d 个寄存器:\n", count);
    for (int i = 0; i < count; i++) {
        if (is32bit){
            debug_printf("寄存器[0x%04X] = %f\n",
                   i, hex_to_float(registers[i]));
        }else{
            debug_printf("寄存器[0x%04X] = 0x%04X (%d)\n",
                   i, registers[i], registers[i]);
        }
    }
#endif
}


std::vector<std::string> stringSplit(const std::string& str, char delim) {
    std::string s;
    s.append(1, delim);
    std::regex reg(s);
    std::vector<std::string> elems(std::sregex_token_iterator(str.begin(), str.end(), reg, -1),
                                   std::sregex_token_iterator());
    return elems;
}

// 计算CRC校验
unsigned short calculate_crc(unsigned char *buf, int len) {
    unsigned short crc = 0xFFFF;
    int i, j;

    for (i = 0; i < len; i++) {
        crc ^= (unsigned short)buf[i];
        for (j = 0; j < 8; j++) {
            if ((crc & 0x0001) != 0) {
                crc >>= 1;
                crc ^= 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

// 清理串口缓冲区，避免DCS数据干扰烟气分析仪
void clear_serial_buffer(int fd) {
#ifdef _WIN32
    HANDLE hSerial = (HANDLE)(intptr_t)fd;
    if (hSerial != INVALID_HANDLE_VALUE) {
        // 清空输入和输出缓冲区
        PurgeComm(hSerial, PURGE_RXCLEAR | PURGE_TXCLEAR);
        debug_printf("烟气分析仪串口缓冲区已清理\n");
    }
#else
    // Linux下清空缓冲区
    tcflush(fd, TCIOFLUSH);
    debug_printf("烟气分析仪串口缓冲区已清理\n");
#endif
}

// 发送Modbus请求并接收响应
int modbus_request(int fd, unsigned char slave_addr, unsigned char func_code,
                           unsigned short start_addr, unsigned short quantity, unsigned char *response) {
    unsigned char request[256];
    int req_len = 0;
    unsigned short crc;

    // 构建请求
    request[req_len++] = slave_addr;
    request[req_len++] = func_code;
    request[req_len++] = (unsigned char)(start_addr >> 8);
    request[req_len++] = (unsigned char)(start_addr & 0xFF);
    request[req_len++] = (unsigned char)(quantity >> 8);
    request[req_len++] = (unsigned char)(quantity & 0xFF);

    // 计算CRC校验
    crc = calculate_crc(request, req_len);
    request[req_len++] = (unsigned char)(crc & 0xFF);
    request[req_len++] = (unsigned char)(crc >> 8);

    // 发送请求
    int written = write(fd, request, req_len);
    if (written != req_len) {
        debug_perror("发送请求失败");
        return -1;
    }

    // 等待响应
    std::this_thread::sleep_for(std::chrono::milliseconds(150));

    // 接收响应 - 增强的数据过滤机制
    unsigned char temp_buffer[1024];
    int total_read = 0;
    int attempts = 0;
    const int max_attempts = 5;

    // 预期响应长度
    int expected_len = 5 + (func_code == READ_HOLDING_REGISTERS ? quantity * 2 : 0);

    while (attempts < max_attempts) {
        // 清空缓冲区，避免累积过多无关数据
        if (attempts > 0) {
            clear_serial_buffer(fd);
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        int read_len = read(fd, temp_buffer, sizeof(temp_buffer));
        if (read_len > 0) {
            total_read = read_len;  // 每次重新开始，不累积

            // 只在第一次尝试时输出读取信息
            if (attempts == 0) {
                debug_printf("读取到 %d 字节数据\n", read_len);
            }

            // 查找正确的烟气分析仪响应
            for (int i = 0; i <= total_read - expected_len; i++) {
                if (temp_buffer[i] == slave_addr && temp_buffer[i+1] == func_code) {
                    // 检查是否为异常响应
                    if (temp_buffer[i+1] & 0x80) {
                        debug_printf("检测到Modbus异常响应: 功能码=0x%02X, 异常码=0x%02X\n",
                               temp_buffer[i+1] & 0x7F, temp_buffer[i+2]);
                        continue;
                    }

                    // 检查数据长度字段是否正确
                    if (func_code == READ_HOLDING_REGISTERS && temp_buffer[i+2] == quantity * 2) {
                        // 复制完整响应到输出缓冲区
                        memcpy(response, &temp_buffer[i], expected_len);

                        // 验证CRC校验
                        unsigned short received_crc = (response[expected_len-2] | (response[expected_len-1] << 8));
                        unsigned short calculated_crc = calculate_crc(response, expected_len - 2);
                        if (received_crc == calculated_crc) {
                            return expected_len;
                        } else {
                            debug_printf("CRC校验失败: 接收=0x%04X, 计算=0x%04X\n", received_crc, calculated_crc);
                        }
                    }
                }
            }
        }

        attempts++;
        if (attempts < max_attempts) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    debug_printf("烟气分析仪通讯失败，设备地址=%d\n", slave_addr);
    return -1;
}

// 读取保持寄存器
int read_holding_registers(int fd, unsigned char slave_addr,
                                   unsigned short start_addr, unsigned short quantity,
                                   unsigned int *registers,bool is32bit) {
    unsigned char response[256] = {0};
    int resp_len = modbus_request(fd, slave_addr, READ_HOLDING_REGISTERS,
                                  start_addr, quantity, response);

    int real_count = 0;
    if (resp_len < 0) {
        return -1;
    }

    // 解析响应
    int byte_count = response[2];
    if (byte_count != quantity * 2) {
        debug_printf("寄存器数量不匹配: 请求=%d, 响应字节数=%d\n", quantity, byte_count);
        return -1;
    }

    if (is32bit){//32位浮点数获取,连续取两个寄存器数据拼成32位
        int k = 0,j=0;
        for (int i = 0; i < quantity; i++) {
            registers[j] = registers[j] <<16 | (response[3 + i*2] << 8) | response[4 + i*2];
            if (k++ == 1) {
                k = 0;
                j++;
                real_count++;
            }
        }
    }else{
        for (int i = 0; i < quantity; i++) {
            registers[i] = (response[3 + i*2] << 8) | response[4 + i*2];
            real_count++;
        }
    }

    if (real_count > 0) {
        print_result(registers, real_count, is32bit);
    }

    return real_count;
}



// 打开串口设备
int open_serial_port(const char *device, int speed, char parity,int stop_bits, int data_bits) {
#ifdef _WIN32
    // Windows串口实现
    HANDLE hSerial;
    DCB dcbSerialParams = {0};
    COMMTIMEOUTS timeouts = {0};

    // 打开串口
    hSerial = CreateFileA(device,
                         GENERIC_READ | GENERIC_WRITE,
                         0,
                         NULL,
                         OPEN_EXISTING,
                         FILE_ATTRIBUTE_NORMAL,
                         NULL);

    if (hSerial == INVALID_HANDLE_VALUE) {
        debug_printf("无法打开串口设备: %s\n", device);
        return -1;
    }

    // 设置串口参数
    dcbSerialParams.DCBlength = sizeof(dcbSerialParams);
    if (!GetCommState(hSerial, &dcbSerialParams)) {
        debug_printf("获取串口状态失败\n");
        CloseHandle(hSerial);
        return -1;
    }

    // 设置波特率
    switch(speed) {
        case 9600: dcbSerialParams.BaudRate = CBR_9600; break;
        case 19200: dcbSerialParams.BaudRate = CBR_19200; break;
        case 38400: dcbSerialParams.BaudRate = CBR_38400; break;
        case 57600: dcbSerialParams.BaudRate = CBR_57600; break;
        case 115200: dcbSerialParams.BaudRate = CBR_115200; break;
        default: dcbSerialParams.BaudRate = CBR_9600; break;
    }

    // 设置数据位
    dcbSerialParams.ByteSize = data_bits;

    // 设置停止位
    dcbSerialParams.StopBits = (stop_bits == 1) ? ONESTOPBIT : TWOSTOPBITS;

    // 设置校验位
    switch(parity) {
        case 'N': dcbSerialParams.Parity = NOPARITY; break;
        case 'E': dcbSerialParams.Parity = EVENPARITY; break;
        case 'O': dcbSerialParams.Parity = ODDPARITY; break;
        default: dcbSerialParams.Parity = NOPARITY; break;
    }

    if (!SetCommState(hSerial, &dcbSerialParams)) {
        debug_printf("设置串口参数失败\n");
        CloseHandle(hSerial);
        return -1;
    }

    // 设置超时
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = 50;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 50;
    timeouts.WriteTotalTimeoutMultiplier = 10;

    if (!SetCommTimeouts(hSerial, &timeouts)) {
        debug_printf("设置串口超时失败\n");
        CloseHandle(hSerial);
        return -1;
    }

    debug_printf("Windows串口 %s 打开成功\n", device);
    return (int)(intptr_t)hSerial; // 将HANDLE转换为int返回
#else
    int fd = open(device, O_RDWR | O_NOCTTY | O_NDELAY);
    if (fd == -1) {
        debug_perror("无法打开串口设备");
        return -1;
    }

    // 设置串口属性
    struct termios options;
    tcgetattr(fd, &options);
    cfsetispeed(&options, speed);
    cfsetospeed(&options, speed);

    // 8数据位，1停止位，无校验
    if (parity == 'N')
        options.c_cflag &= ~PARENB;
    else if (parity == 'E') {
        options.c_cflag |= PARENB;
        options.c_cflag &= ~PARODD;
    } else if (parity == 'O') {
        options.c_cflag |= PARENB;
        options.c_cflag |= PARODD;
    } else {
        debug_perror("不支持的校验位");
        close(fd);
        return -1;
    }

    // 设置停止位
    if (stop_bits == 1)
        options.c_cflag &= ~CSTOPB;
    else if (stop_bits == 2)
        options.c_cflag |= CSTOPB;
    else {
        debug_perror("不支持的停止位");
        close(fd);
        return -1;
    }

    // 设置数据位
    options.c_cflag &= ~CSIZE;
    if (data_bits == 8)
        options.c_cflag |= CS8;
    else if (data_bits == 7)
        options.c_cflag |= CS7;
    else if (data_bits == 6)
        options.c_cflag |= CS6;
    else if (data_bits == 5)
        options.c_cflag |= CS5;
    else {
        debug_printf("不支持的数据位: %d\n", data_bits);
        close(fd);
        return -1;
    }

    // 设置本地模式和阻塞读取
    options.c_cflag |= (CLOCAL | CREAD);
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    options.c_oflag &= ~OPOST;
    options.c_iflag &= ~(IXON | IXOFF | IXANY);

    // 设置超时
    options.c_cc[VMIN] = 0;
    options.c_cc[VTIME] = 10;

    // 应用设置
    tcsetattr(fd, TCSANOW, &options);

    // 清空缓冲区
    tcflush(fd, TCIOFLUSH);

    return fd;
#endif
}

//启动文件句柄
std::unordered_map<std::string, int>  start_fd(ConfigManager *config){
    //读取protocollist配置
    std::string protocol_list = config->get<std::string>("ProtocolList","list");
    std::unordered_map<std::string, int> protocol_fd_map;
    std::vector<std::string> vect_protocol = stringSplit(protocol_list,',');

    for (const auto& protocol_config_name : vect_protocol) {
        //读取通信配置
        std::string port = config->get<std::string>(protocol_config_name, "Port");
        int baud_rate = config->get<int>(protocol_config_name, "BaudRate", 9600);  // 默认值
        char parity = config->get<char>(protocol_config_name, "Parity");//N/E/O
        int stop_bits = config->get<int>(protocol_config_name, "StopBits", 1);  // 默认值
        int data_bits = config->get<int>(protocol_config_name, "DataBits", 8);  // 默认值

        debug_printf("正在尝试打开串口: %s, 波特率: %d, 校验: %c, 停止位: %d, 数据位: %d\n",
               port.c_str(), baud_rate, parity, stop_bits, data_bits);

        int fd = open_serial_port(port.c_str(),baud_rate, parity, stop_bits, data_bits);
        if (fd >= 0) {
            debug_printf("串口 %s 打开成功，句柄: %d\n", port.c_str(), fd);
        } else {
            debug_printf("串口 %s 打开失败\n", port.c_str());
        }
        protocol_fd_map[protocol_config_name]  = fd;
    }
    return protocol_fd_map;
}

std::unordered_map<std::string, Boiler *>  get_boiler_list(ConfigManager *config){
    //读取boildrelist配置
    std::string boiler_list = config->get<std::string>("BoilerList","list");
    debug_printf("调试: 从配置读取的锅炉列表: '%s'\n", boiler_list.c_str());

    std::unordered_map<std::string, Boiler *> boiler_map;
    std::vector<std::string> vect_boiler = stringSplit(boiler_list,',');

    debug_printf("调试: 解析后的锅炉数量: %zu\n", vect_boiler.size());

    for (const auto& boiler_name : vect_boiler) {
        debug_printf("调试: 创建锅炉: '%s'\n", boiler_name.c_str());
        Boiler* boiler = new Boiler(config,boiler_name);

        // 检查锅炉是否成功初始化
        if (boiler->is_initialized) {
            boiler_map[boiler_name] = boiler;
            debug_printf("调试: 锅炉 '%s' 创建并初始化成功\n", boiler_name.c_str());
        } else {
            debug_printf("错误: 锅炉 '%s' 初始化失败，跳过该锅炉\n", boiler_name.c_str());
            delete boiler;  // 释放内存
        }
    }

    debug_printf("调试: 总共创建了 %zu 个锅炉\n", boiler_map.size());
    return boiler_map;
}

/**
 * @brief 压力表数据解析函数
 * @param raw_data 从压力表读取的16位原始数据
 * @return 解析后的压力值（单位：根据压力表配置，通常为Pa或kPa）
 * @details 根据压力表报文解析文档：
 *          - 请求报文：32 03 10 00 00 01 85 09 (向设备50请求读取地址4096的1个寄存器)
 *          - 响应报文：32 03 02 FF 9C ED D9
 *          - 寄存器数据：FF 9C（大端序）→ 0xFF9C
 *          - 有符号整数（补码表示）：最高位为1→负数
 *          - 补码计算：0xFF9C = -(0x10000 - 0xFF9C) = -(0x64) = -100
 *          - 软件读数（计量单位0.1）：-100 * 0.1 = -10.0
 */
float parse_pressure_data(uint16_t raw_data) {
    int16_t signed_pressure_value;

    // 检查最高位判断正负数
    if (raw_data >= 0x8000) {
        // 最高位为1，表示负数，进行补码转换
        signed_pressure_value = -(int16_t)(0x10000 - raw_data);
    } else {
        // 最高位为0，表示正数
        signed_pressure_value = (int16_t)raw_data;
    }

    // 应用计量单位0.1的转换，得到实际压力值
    float pressure_value = signed_pressure_value * 0.1f;

    debug_printf("压力表数据解析: 原始16位值=0x%04X, 有符号值=%d, 最终压力值=%.1f\n",
               raw_data, signed_pressure_value, pressure_value);

    return pressure_value;
}

//根据锅炉名获取数据（实时采集模式）
void get_realtime_data(std::string boiler_name,float *pco,float *po2,float *pcurrent,float *pvoltage,float *ptemperature,int *pswitch1){
    // 不使用全局rwMutex，而是使用锅炉对象的rwMutex

    auto it = boiler_map.find(boiler_name);
    if (it == boiler_map.end() || it->second == NULL) {
        debug_printf("错误: 找不到锅炉 '%s'\n", boiler_name.c_str());
        return;
    }

    Boiler *boiler = it->second;

    // 检查串口是否有效
    if (boiler->fd < 0) {
        *pco = *po2 = *pcurrent = *pvoltage = *ptemperature = 0.0f;
        *pswitch1 = 0;  // 反吹反馈信号设为停止状态
        return;
    }

    // 返回采集到的数据（使用互斥锁保证线程安全）
    {
        std::lock_guard<std::mutex> data_lock(boiler->rwMutex);
        *pco = boiler->co;
        *po2 = boiler->o2;
        *pcurrent = boiler->current;
        *pvoltage = boiler->voltage;
        *ptemperature = boiler->temperature;
        *pswitch1 = boiler->switch1;
    }
}


