#include "parameter_adjustment.h"
#include "parameter_adjustment_qml.h"
#include "dcsopc.h"
#include "boiler.h"
#include <cstring>
#include <cmath>
#include <QMetaObject>

// 声明在data.cpp中定义的调试函数
extern void debug_printf(const char* format, ...);

// 声明全局配置管理器
extern ConfigManager* g_config_manager;

// 全局参数调整管理器实例
ParameterAdjustmentManager* g_parameter_adjustment_manager = nullptr;

// 全局QML包装类实例指针（用于发出信号）
ParameterAdjustmentQML* g_parameter_adjustment_qml = nullptr;

/**
 * @brief 参数调整管理器构造函数
 * @param config_manager 配置管理器指针，用于读取调整参数配置
 *
 * 功能说明：
 * 1. 初始化各种参数调整的配置参数（阈值、步长、写入地址等）
 * 2. 设置调整状态为禁用状态
 * 3. 重置调整间隔计数器
 */
ParameterAdjustmentManager::ParameterAdjustmentManager(ConfigManager *config_manager)
    : config_manager(config_manager), auto_adjustment_enabled(false),
      adjustment_counter(0) {

    // 从配置文件加载自动调整模式设置
    auto_adjustment_enabled = config_manager->get<bool>("ParameterAdjustment", "AutoAdjustmentEnabled", false);

    // 加载调整间隔配置参数
    adjustment_check_interval = config_manager->get<int>("ParameterAdjustment", "AdjustmentCheckInterval", 3);

    // 加载生料量调整配置参数
    raw_material_diff_threshold = config_manager->get<float>("ParameterAdjustment", "RawMaterialDiffThreshold", 10.0f);
    raw_material_adjustment_step = config_manager->get<float>("ParameterAdjustment", "RawMaterialAdjustmentStep", 5.0f);

    // 加载炉膛温度调整配置参数
    furnace_temp_diff_threshold = config_manager->get<float>("ParameterAdjustment", "FurnaceTempDiffThreshold", 10.0f);
    furnace_temp_adjustment_step = config_manager->get<float>("ParameterAdjustment", "FurnaceTempAdjustmentStep", 10.0f);
    furnace_set_temp_write_address = config_manager->get<int>("ParameterAdjustment", "FurnaceSetTempWriteAddress", 101);
    furnace_temp_max_value = config_manager->get<float>("ParameterAdjustment", "FurnaceTempMaxValue", 1200.0f);
    furnace_temp_min_value = config_manager->get<float>("ParameterAdjustment", "FurnaceTempMinValue", 800.0f);

    // 加载给煤量调整配置参数
    coal_feed_diff_threshold = config_manager->get<float>("ParameterAdjustment", "CoalFeedDiffThreshold", 10.0f);
    coal_feed_adjustment_step = config_manager->get<float>("ParameterAdjustment", "CoalFeedAdjustmentStep", 10.0f);
    coal_feed_max_value = config_manager->get<float>("ParameterAdjustment", "CoalFeedMaxValue", 50000.0f);
    coal_feed_min_value = config_manager->get<float>("ParameterAdjustment", "CoalFeedMinValue", 0.0f);

    // 加载氧气浓度调整配置参数
    oxygen_concentration_diff_threshold = config_manager->get<float>("ParameterAdjustment", "OxygenConcentrationDiffThreshold", 0.1f);
    oxygen_concentration_setpoint = config_manager->get<float>("ParameterAdjustment", "OxygenConcentrationSetpoint", 3.5f);
    oxygen_raw_material_adjustment_step = config_manager->get<float>("ParameterAdjustment", "OxygenRawMaterialAdjustmentStep", 3.0f);
    oxygen_coal_feed_adjustment_step = config_manager->get<float>("ParameterAdjustment", "OxygenCoalFeedAdjustmentStep", 5.0f);

    // 加载炉压调整配置参数
    furnace_pressure_diff_threshold = config_manager->get<float>("ParameterAdjustment", "FurnacePressureDiffThreshold", 1.0f);
    furnace_pressure_setpoint = config_manager->get<float>("ParameterAdjustment", "FurnacePressureSetpoint", -75.0f);
    furnace_pressure_setpoint_min = config_manager->get<float>("ParameterAdjustment", "FurnacePressureSetpointMin", -100.0f);
    furnace_pressure_setpoint_max = config_manager->get<float>("ParameterAdjustment", "FurnacePressureSetpointMax", -50.0f);
    furnace_pressure_min_value = config_manager->get<float>("ParameterAdjustment", "FurnacePressureMinValue", -120.0f);
    furnace_pressure_max_value = config_manager->get<float>("ParameterAdjustment", "FurnacePressureMaxValue", -30.0f);
    furnace_pressure_write_address = config_manager->get<int>("ParameterAdjustment", "FurnacePressureWriteAddress", 103);

    // 加载引风机调整配置参数
    induced_draft_fan_adjustment_step = config_manager->get<float>("ParameterAdjustment", "InducedDraftFanAdjustmentStep", 0.1f);
    induced_draft_fan_max_speed = config_manager->get<float>("ParameterAdjustment", "InducedDraftFanMaxSpeed", 50.0f);
    induced_draft_fan_min_speed = config_manager->get<float>("ParameterAdjustment", "InducedDraftFanMinSpeed", 10.0f);

    // 加载临时参数配置
    temp_furnace_set_temp = config_manager->get<float>("DCS1", "TempFurnaceSetTemp", 1000.0f);
    temp_planned_raw_material = config_manager->get<float>("DCS1", "TempPlannedRawMaterial", 5.0f);

    // 加载生料量设定目标选择配置
    raw_material_setpoint_target = config_manager->get<int>("ParameterAdjustment", "RawMaterialSetpointTarget", 2);

    last_adjustment_time = std::chrono::steady_clock::now();

    // 初始化当前调整建议
    current_suggestion.has_suggestion = false;

    debug_printf("参数调整管理器初始化完成\n");
    debug_printf("调整间隔配置 - 参数调整: %d次采集\n", adjustment_check_interval);
    debug_printf("生料量调整配置 - 差值阈值: %.1fkg/h, 调节步长: %.1fkg/h\n",
                raw_material_diff_threshold, raw_material_adjustment_step);
    debug_printf("炉膛温度调整配置 - 差值阈值: %.1f℃, 调节步长: %.1f℃, DCS写入地址: %d, 范围: %.1f-%.1f℃\n",
                furnace_temp_diff_threshold, furnace_temp_adjustment_step, furnace_set_temp_write_address,
                furnace_temp_min_value, furnace_temp_max_value);
    debug_printf("给煤量调整配置 - 差值阈值: %.1fkg/h, 调节步长: %.1fkg(%.3ft/h), 范围: %.3f-%.3ft/h\n",
                coal_feed_diff_threshold, coal_feed_adjustment_step, coal_feed_adjustment_step/1000.0f,
                coal_feed_min_value, coal_feed_max_value);
    debug_printf("炉压调整配置 - 差值阈值: %.1fPa, 给定炉压: %.1fPa, DCS写入地址: %d, 范围: %.1f-%.1fPa\n",
                furnace_pressure_diff_threshold, furnace_pressure_setpoint, furnace_pressure_write_address,
                furnace_pressure_min_value, furnace_pressure_max_value);
}

// 析构函数
ParameterAdjustmentManager::~ParameterAdjustmentManager() {
    debug_printf("参数调整管理器销毁\n");
}

// 初始化调整管理器
int ParameterAdjustmentManager::initialize() {
    debug_printf("参数调整管理器初始化完成\n");
    return 0;
}

/**
 * @brief 启用或禁用自动调整模式
 * @param enable true=启用自动调整，false=禁用自动调整（手动模式）
 *
 * 功能说明：
 * 1. 自动调整模式：计算调整建议值并发送DCS指令
 * 2. 手动调整模式：只计算调整建议值，不发送DCS指令
 * 3. 重置调整间隔计数器
 */
void ParameterAdjustmentManager::enable_auto_adjustment(bool enable) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);
    auto_adjustment_enabled = enable;

    // 保存设置到配置文件
    save_auto_adjustment_setting(enable);

    // 重置调整间隔计数器
    adjustment_counter = 0;

    debug_printf("调整模式切换 - %s模式已启用\n", enable ? "自动调整" : "手动调整");
    debug_printf("调整间隔计数器已重置\n");
}

/**
 * @brief 保存自动调整模式设置到配置文件
 * @param enable 是否启用自动调整模式
 *
 * 功能说明：
 * 1. 将自动调整模式设置保存到config.ini文件
 * 2. 确保下次启动时保持用户的选择
 * 3. 保存格式与CollectionConfigView.qml一致（"true"/"false"）
 */
void ParameterAdjustmentManager::save_auto_adjustment_setting(bool enable) {
    if (config_manager) {
        // 使用字符串格式保存，与CollectionConfigView.qml保持一致
        std::string value = enable ? "true" : "false";
        config_manager->set<std::string>("ParameterAdjustment", "AutoAdjustmentEnabled", value);
        if (config_manager->save()) {
            debug_printf("自动调整模式设置已保存到配置文件: %s\n", value.c_str());
        } else {
            debug_printf("错误: 自动调整模式设置更新成功但保存到文件失败: %s\n", value.c_str());
        }
    }
}
/**
 * @brief 设置给定炉压
 * @param pressure 给定炉压值（Pa）
 *
 * 功能说明：
 * 1. 设置炉压调节的目标值 todo 这里的范围要估计config.ini中的配置来定
 * 2. 验证输入范围（-100Pa 到 -50Pa）
 * 3. 保存到配置文件
 */
void ParameterAdjustmentManager::set_furnace_pressure_setpoint(float pressure) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);

    // 验证输入范围
    if (pressure < furnace_pressure_setpoint_min || pressure > furnace_pressure_setpoint_max) {
        debug_printf("错误: 给定炉压超出范围 [%.1f, %.1f]Pa，输入值: %.1fPa\n",
                    furnace_pressure_setpoint_min, furnace_pressure_setpoint_max, pressure);
        return;
    }

    furnace_pressure_setpoint = pressure;

    // 保存到配置文件
    if (config_manager) {
        config_manager->set("ParameterAdjustment", "FurnacePressureSetpoint", pressure);
        if (config_manager->save()) {
            debug_printf("给定炉压已更新并保存到文件: %.1fPa\n", pressure);
        } else {
            debug_printf("错误: 给定炉压更新成功但保存到文件失败: %.1fPa\n", pressure);
        }
    }
}

/**
 * @brief 设置设定氧气浓度
 * @param concentration 设定氧气浓度值（%）
 *
 * 功能说明：todo 这里的范围要估计config.ini中的配置来定
 * 1. 设置氧气浓度调节的目标值
 * 2. 验证输入范围（0% 到 21%）
 * 3. 保存到配置文件
 */
void ParameterAdjustmentManager::set_oxygen_concentration_setpoint(float concentration) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);

    // 验证输入范围（氧气浓度应在0-21%之间）
    if (concentration < 0.0f || concentration > 21.0f) {
        debug_printf("错误: 设定氧气浓度超出范围 [0.0, 21.0]%%，输入值: %.2f%%\n", concentration);
        return;
    }

    oxygen_concentration_setpoint = concentration;

    // 保存到配置文件
    if (config_manager) {
        config_manager->set("ParameterAdjustment", "OxygenConcentrationSetpoint", concentration);
        if (config_manager->save()) {
            debug_printf("设定氧气浓度已更新并保存到文件: %.2f%%\n", concentration);
        } else {
            debug_printf("错误: 设定氧气浓度更新成功但保存到文件失败: %.2f%%\n", concentration);
        }
    }
}

/**
 * @brief 设置临时炉膛设定温度
 * @param temp 临时炉膛设定温度值（℃）
 *
 * 功能说明：
 * 1. 设置临时炉膛设定温度（用于替代空的OPC标签）
 * 2. 验证输入范围（800℃ 到 1200℃）
 * 3. 保存到配置文件
 */
void ParameterAdjustmentManager::set_temp_furnace_set_temp(float temp) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);

    // 验证输入范围
    if (temp < furnace_temp_min_value || temp > furnace_temp_max_value) {
        debug_printf("错误: 临时炉膛设定温度超出范围 [%.1f, %.1f]℃，输入值: %.1f℃\n",
                    furnace_temp_min_value, furnace_temp_max_value, temp);
        return;
    }

    temp_furnace_set_temp = temp;

    // 保存到配置文件
    if (config_manager) {
        config_manager->set("DCS1", "TempFurnaceSetTemp", temp);
        if (config_manager->save()) {
            debug_printf("临时炉膛设定温度已更新并保存到文件: %.1f℃\n", temp);
        } else {
            debug_printf("错误: 临时炉膛设定温度更新成功但保存到文件失败: %.1f℃\n", temp);
        }
    }
}

/**
 * @brief 设置临时计划生料量
 * @param material 临时计划生料量值（t/h）
 *
 * 功能说明：
 * 1. 设置临时计划生料量（用于替代空的OPC标签）
 * 2. 验证输入范围（不能为负数）
 * 3. 保存到配置文件
 */
void ParameterAdjustmentManager::set_temp_planned_raw_material(float material) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);

    // 验证输入范围（不能为负数）
    if (material < 0.0f) {
        debug_printf("错误: 临时计划生料量不能为负数，输入值: %.3ft/h\n", material);
        return;
    }

    temp_planned_raw_material = material;

    // 保存到配置文件
    if (config_manager) {
        config_manager->set("DCS1", "TempPlannedRawMaterial", material);
        if (config_manager->save()) {
            debug_printf("临时计划生料量已更新并保存到文件: %.3ft/h\n", material);
        } else {
            debug_printf("错误: 临时计划生料量更新成功但保存到文件失败: %.3ft/h\n", material);
        }
    }
}

/**
 * @brief 设置生料量设定目标选择
 * @param target 生料量设定调整目标（1=生料量1设定，2=生料量2设定）
 *
 * 功能说明：
 * 1. 设置生料量设定调整目标
 * 2. 验证输入范围（1或2）
 * 3. 保存到配置文件
 */
void ParameterAdjustmentManager::set_raw_material_setpoint_target(int target) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);

    // 验证输入范围（只能是1或2）
    if (target != 1 && target != 2) {
        debug_printf("错误: 生料量设定目标选择超出范围，输入值: %d（有效值：1或2）\n", target);
        return;
    }

    raw_material_setpoint_target = target;

    // 保存到配置文件
    if (config_manager) {
        config_manager->set("ParameterAdjustment", "RawMaterialSetpointTarget", target);
        if (config_manager->save()) {
            debug_printf("生料量设定目标已更新并保存到文件: %d（%s）\n", target,
                        target == 1 ? "生料量1设定" : "生料量2设定");
        } else {
            debug_printf("错误: 生料量设定目标更新成功但保存到文件失败: %d\n", target);
        }
    }
}

/**
 * @brief 检查是否需要调整参数并执行调整
 * @param dcs_name DCS设备名称
 * @param actual_raw_material 实际总生料量（实际生料量1+实际生料量2）
 * @param planned_raw_material 计划生料量
 *
 * 功能说明：
 * 1. 每20秒采集一次数据，生料量和其他参数有独立的调整间隔
 * 2. 实现分层调整逻辑：生料量必须先达标，然后依次调整其他参数
 * 3. 自动模式：计算建议值并发送DCS指令
 * 4. 手动模式：只计算建议值，不发送DCS指令
 * 5. 调整建议针对实际生料量2，但对比使用总生料量与计划生料量
 *
 * 业务流程：
 * - 每20秒采集一次数据
 * - 生料量：按照RawMaterialAdjustmentCheckInterval进行调整检查
 * - 其他参数：按照OtherParametersAdjustmentCheckInterval进行调整检查
 * - 生料量必须达标后才执行后续参数调整
 */
void ParameterAdjustmentManager::check_and_adjust_parameters(const std::string& dcs_name,
                                                           float actual_raw_material,
                                                           float planned_raw_material) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);

    // 增加调整间隔计数器
    adjustment_counter++;

    // === 设备连接状态检查 ===
    // 在执行任何参数调整之前，首先检查DCS和烟气分析仪的连接状态
    // 如果任一设备未连接，则直接结束，不执行后续调整操作

    // 1. 检查DCS OPC设备连接状态
    auto opc_it = dcs_opc_map.find(dcs_name);
    if (opc_it == dcs_opc_map.end() || !opc_it->second) {
        debug_printf("错误: 无法获取DCS OPC设备 '%s' 的数据\n", dcs_name.c_str());
        return;
    }

    DCSOPCDevice* dcs_opc_device = opc_it->second;

    // 检查DCS OPC连接状态
    if (!dcs_opc_device->is_opc_connected()) {
        debug_printf("参数调整: DCS OPC设备 '%s' 未连接，停止所有调整操作\n", dcs_name.c_str());
        return;
    }

    // 2. 检查烟气分析仪连接状态
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    auto boiler_it = boiler_map.find(dcs_opc_device->associated_boiler);
    if (boiler_it == boiler_map.end() || !boiler_it->second) {
        debug_printf("参数调整: 无法获取关联烟气分析仪 '%s' 的数据，停止所有调整操作\n",
                    dcs_opc_device->associated_boiler.c_str());
        return;
    }

    Boiler* boiler_device = boiler_it->second;

    // 检查烟气分析仪连接状态（fd >= 0 表示串口连接正常）
    if (boiler_device->fd < 0) {
        debug_printf("参数调整: 烟气分析仪 '%s' 未连接（fd=%d），停止所有调整操作\n",
                    dcs_opc_device->associated_boiler.c_str(), boiler_device->fd);
        return;
    }

    debug_printf("参数调整: 设备连接检查通过 - DCS OPC: %s (已连接), 烟气分析仪: %s (fd=%d)\n",
                dcs_name.c_str(), dcs_opc_device->associated_boiler.c_str(), boiler_device->fd);

    // === 获取设备数据 ===
    float furnace_set_temp, furnace_actual_temp, actual_furnace_pressure, coal_feed_rate;
    float coal_feed_set, raw_material1_set, raw_material2_set, induced_draft_fan_set;

    // 获取当前DCS参数值（加锁保护）
    {
        std::lock_guard<std::mutex> dcs_lock(dcs_opc_device->rwMutex);
        // 如果OPC标签为空，使用临时炉膛设定温度
        if (dcs_opc_device->opc_furnace_set_temp_tag.empty() || dcs_opc_device->furnace_set_temp == 0.0f) {
            furnace_set_temp = temp_furnace_set_temp;
            debug_printf("使用临时炉膛设定温度: %.1f℃ (OPC标签为空)\n", temp_furnace_set_temp);
        } else {
            furnace_set_temp = dcs_opc_device->furnace_set_temp;
        }
        furnace_actual_temp = dcs_opc_device->furnace_actual_temp;
        actual_furnace_pressure = dcs_opc_device->actual_furnace_pressure;
        coal_feed_rate = dcs_opc_device->coal_feed_rate;

        // 获取设定值数据
        coal_feed_set = dcs_opc_device->coal_feed_set;
        raw_material1_set = dcs_opc_device->raw_material1_set;
        raw_material2_set = dcs_opc_device->raw_material2_set;
        induced_draft_fan_set = dcs_opc_device->induced_draft_fan_set;
    }

    // 获取氧气浓度数据（从已检查连接的烟气分析仪获取）
    float oxygen_concentration = 0.0f;
    bool oxygen_data_valid = false;
    {
        std::lock_guard<std::mutex> boiler_lock(boiler_device->rwMutex);
        oxygen_concentration = boiler_device->o2;  // 从烟气分析仪获取氧气浓度
        // 检查氧气浓度数据有效性
        // 1. 数值范围检查：氧气浓度大于等于0即可（0值通常表示设备故障或通讯异常）
        // 2. 数据准备状态检查：确保设备已完成至少一次完整的数据采集
        oxygen_data_valid = (oxygen_concentration >= 0.0f && boiler_device->data_ready);
        debug_printf("从烟气分析仪获取氧气浓度: %.2f%%, 数据准备状态: %s, 数据有效性: %s\n",
                    oxygen_concentration, boiler_device->data_ready ? "已准备" : "未准备",
                    oxygen_data_valid ? "有效" : "无效");
    }

    // 获取计划生料量（如果OPC标签为空，使用临时计划生料量）
    float effective_planned_raw_material = planned_raw_material;
    bool using_temp_planned = false;
    if (dcs_opc_device->opc_planned_raw_material_tag.empty() || planned_raw_material == 0.0f) {
        effective_planned_raw_material = temp_planned_raw_material;
        using_temp_planned = true;
        debug_printf("使用临时计划生料量: %.3ft/h (OPC标签为空)\n", temp_planned_raw_material);
    }

    // 分层调整逻辑：生料量必须先达标，然后依次调整其他参数
    bool raw_material_compliant = false;

    // 检查参数调整间隔
    if (adjustment_counter >= adjustment_check_interval) {
        debug_printf("=== 参数调整检查周期 - DCS: %s ===\n", dcs_name.c_str());
        adjustment_counter = 0;  // 重置计数器

        // 清空之前的调整建议（已经在外层加锁，不需要重复加锁）
        current_suggestion.has_suggestion = false;

        debug_printf("生料量状态 - 实际: %.3ft/h, 计划: %.3ft/h\n", actual_raw_material, effective_planned_raw_material);

        // 检查生料量是否达标
        // 统一转换为kg单位进行计算，避免精度丢失
        // 注意：现在所有DCS数据都是t/h单位，需要转换为kg/h进行内部计算
        float actual_raw_material_kg = actual_raw_material * 1000.0f; // t/h转换为kg/h
        float effective_planned_raw_material_kg = effective_planned_raw_material;

        if (using_temp_planned) {
            // 临时计划生料量使用t/h单位，需要转换为kg/h
            effective_planned_raw_material_kg = effective_planned_raw_material * 1000.0f; // t/h转换为kg/h
        } else {
            // DCS计划生料量现在也是t/h单位，需要转换为kg/h
            effective_planned_raw_material_kg = effective_planned_raw_material * 1000.0f; // t/h转换为kg/h
        }

        float raw_material_diff_kg = std::abs(actual_raw_material_kg - effective_planned_raw_material_kg);
        raw_material_compliant = (raw_material_diff_kg <= raw_material_diff_threshold);

        if (using_temp_planned) {
            debug_printf("生料量状态 - 实际: %.3fkg/h, 计划: %.3ft/h (%.3fkg/h), 差值: %.3fkg, 阈值: %.1fkg, 达标状态: %s\n",
                        actual_raw_material_kg, effective_planned_raw_material, effective_planned_raw_material_kg,
                        raw_material_diff_kg, raw_material_diff_threshold,
                        raw_material_compliant ? "达标" : "未达标");
        } else {
            debug_printf("生料量状态 - 实际: %.3fkg/h, 计划: %.3fkg/h, 差值: %.3fkg, 阈值: %.1fkg, 达标状态: %s\n",
                        actual_raw_material_kg, effective_planned_raw_material_kg,
                        raw_material_diff_kg, raw_material_diff_threshold,
                        raw_material_compliant ? "达标" : "未达标");
        }

        // 1. 首先执行生料量调节（如果需要）
        if (!raw_material_compliant) {
            debug_printf("生料量未达标，执行生料量调节\n");
            // 根据配置选择调整生料量1设定还是生料量2设定
            float target_raw_material_set = (raw_material_setpoint_target == 1) ? raw_material1_set : raw_material2_set;
            adjust_raw_material_parameter(dcs_name, actual_raw_material, effective_planned_raw_material, using_temp_planned, target_raw_material_set);
            return; // 本次调整周期结束，等待下次周期重新检查
        }

        // 2. 生料量达标后，执行其他参数调节
        debug_printf("生料量已达标，执行其他参数调节\n");
        debug_printf("当前参数状态 - 炉膛设定温度: %.1f℃, 炉膛实际温度: %.1f℃, 实际炉压: %.1fPa, 给煤量: %.3ft/h, 氧气浓度: %.2f%%\n",
                    furnace_set_temp, furnace_actual_temp, actual_furnace_pressure, coal_feed_rate, oxygen_concentration);

        // 重要：每次调整周期只执行一个调节动作，因为DCS执行需要时间
        // 下次调整周期会重新判断所有参数状态，确保调节的准确性
        // 按照业务流程顺序执行调节，每个步骤执行完都回到步骤0重新判断：

        // === 步骤1：判断炉膛温度差值 ===
        float temp_diff = std::abs(furnace_actual_temp - furnace_set_temp);
        bool temp_in_range = (temp_diff <= furnace_temp_diff_threshold);

        debug_printf("=== 步骤1：炉膛温度判断 ===\n");
        debug_printf("炉膛实际温度: %.1f℃, 设定温度: %.1f℃, 差值: %.1f℃, 阈值: ±%.1f℃\n",
                    furnace_actual_temp, furnace_set_temp, temp_diff, furnace_temp_diff_threshold);
        debug_printf("炉膛温度是否在范围内: %s\n", temp_in_range ? "是" : "否");

        if (!temp_in_range) {
            // 超出±10度：调用给煤量调节方法，下次周期回到步骤0
            debug_printf("[步骤1：给煤量调节] 炉膛温度超出范围，调用给煤量调节方法\n");
            adjust_coal_feed_parameter(dcs_name, furnace_set_temp, furnace_actual_temp, coal_feed_set);
            return; // 本次调整周期结束，等待下次周期重新检查
        } else {
            // 在±10度内：进入步骤2
            debug_printf("炉膛温度在范围内，进入步骤2\n");

            // === 步骤2：判断氧气浓度差值 ===
            debug_printf("=== 步骤2：氧气浓度判断 ===\n");
            debug_printf("当前氧气浓度: %.2f%%, 设定氧气浓度: %.2f%%, 数据有效性: %s\n",
                        oxygen_concentration, oxygen_concentration_setpoint, oxygen_data_valid ? "有效" : "无效");

            if (!oxygen_data_valid) {
                // 氧气浓度数据无效，结束本次调整周期，等待下次周期重新检查
                debug_printf("[步骤2：氧气浓度调节] 氧气浓度数据无效，结束本次调整周期，等待下次周期重新检查\n");
                return; // 本次调整周期结束，等待下次周期重新检查
            }

            // 氧气浓度数据有效，进行正常判断
            float oxygen_diff = std::abs(oxygen_concentration - oxygen_concentration_setpoint);
            bool oxygen_in_range = (oxygen_diff <= oxygen_concentration_diff_threshold);

            debug_printf("氧气浓度差值: %.2f%%, 阈值: ±%.2f%%\n", oxygen_diff, oxygen_concentration_diff_threshold);
            debug_printf("氧气浓度是否在范围内: %s\n", oxygen_in_range ? "是" : "否");

            if (!oxygen_in_range) {
                // 超出范围：调用氧气浓度调节方法，下次周期回到步骤0
                debug_printf("[步骤2：氧气浓度调节] 氧气浓度超出范围，调用氧气浓度调节方法\n");
                // 根据配置选择调整生料量1设定还是生料量2设定
                float target_raw_material_set = (raw_material_setpoint_target == 1) ? raw_material1_set : raw_material2_set;
                adjust_oxygen_concentration_parameter(dcs_name, oxygen_concentration, coal_feed_set, target_raw_material_set);
                return; // 本次调整周期结束，等待下次周期重新检查
            } else {
                // 在范围内：进入步骤3
                debug_printf("氧气浓度在范围内，进入步骤3\n");

                // === 步骤3：判断炉压差值 ===
                float pressure_diff = std::abs(actual_furnace_pressure - furnace_pressure_setpoint);
                bool pressure_in_range = (pressure_diff <= furnace_pressure_diff_threshold);

                debug_printf("=== 步骤3：炉压判断 ===\n");
                debug_printf("实际炉压: %.1fPa, 给定炉压: %.1fPa, 差值: %.1fPa, 阈值: ±%.1fPa\n",
                            actual_furnace_pressure, furnace_pressure_setpoint, pressure_diff, furnace_pressure_diff_threshold);
                debug_printf("炉压是否在范围内: %s\n", pressure_in_range ? "是" : "否");

                if (!pressure_in_range) {
                    // 超出范围：调用引风机调节方法，下次周期回到步骤0
                    debug_printf("[步骤3：引风机调节] 炉压超出范围，调用引风机调节方法\n");
                    adjust_induced_draft_fan_parameter(dcs_name, actual_furnace_pressure, induced_draft_fan_set);
                    return; // 本次调整周期结束，等待下次周期重新检查
                } else {
                    // 在范围内：系统达产
                    debug_printf("=== 所有参数都在范围内，系统达产 ===\n");
                }
            }
        }
    } else {
        // 不在调整周期，只检查生料量是否达标（用于状态显示）
        // 统一转换为kg单位进行计算，避免精度丢失
        // 注意：现在所有DCS数据都是t/h单位，需要转换为kg/h进行内部计算
        float actual_raw_material_kg = actual_raw_material * 1000.0f; // t/h转换为kg/h
        float effective_planned_raw_material_kg = effective_planned_raw_material;

        if (using_temp_planned) {
            // 临时计划生料量使用t/h单位，需要转换为kg/h
            effective_planned_raw_material_kg = effective_planned_raw_material * 1000.0f; // t/h转换为kg/h
        } else {
            // DCS计划生料量现在也是t/h单位，需要转换为kg/h
            effective_planned_raw_material_kg = effective_planned_raw_material * 1000.0f; // t/h转换为kg/h
        }

        float raw_material_diff_kg = std::abs(actual_raw_material_kg - effective_planned_raw_material_kg);
        raw_material_compliant = (raw_material_diff_kg <= raw_material_diff_threshold);
    }
}

/**
 * @brief 存储调整建议的内部方法
 * @param parameter_name 参数名称
 * @param suggested_value 建议值
 * @param current_value 当前值
 * @param unit 单位
 * @param reason 调整原因
 */
void ParameterAdjustmentManager::store_adjustment_suggestion_internal(const std::string& parameter_name,
                                                                    float suggested_value,
                                                                    float current_value,
                                                                    const std::string& unit,
                                                                    const std::string& reason) {
    current_suggestion.has_suggestion = true;
    current_suggestion.parameter_name = parameter_name;
    current_suggestion.suggested_value = suggested_value;
    current_suggestion.current_value = current_value;
    current_suggestion.unit = unit;
    current_suggestion.reason = reason;
    current_suggestion.timestamp = std::chrono::steady_clock::now();

    debug_printf("存储调整建议 - 参数: %s, 当前值: %.3f%s, 建议值: %.3f%s, 原因: %s\n",
                parameter_name.c_str(), current_value, unit.c_str(),
                suggested_value, unit.c_str(), reason.c_str());

    // 发出QML信号通知界面更新
    if (g_parameter_adjustment_qml) {
        // 使用Qt的信号机制通知QML界面有新的调整建议
        QMetaObject::invokeMethod(g_parameter_adjustment_qml, "adjustmentSuggestionUpdated",
                                Qt::QueuedConnection,
                                Q_ARG(QString, QString::fromStdString("DCS1")), // TODO: 使用实际的DCS名称
                                Q_ARG(bool, true),
                                Q_ARG(float, suggested_value));
        debug_printf("已发出调整建议更新信号到QML界面\n");
    }
}

/**
 * @brief 存储生料量设定调整建议（同时计算两个生料量设定的建议）
 * @param raw_material1_set_suggested 生料量1设定建议值
 * @param raw_material1_set_current 生料量1设定当前值
 * @param raw_material2_set_suggested 生料量2设定建议值
 * @param raw_material2_set_current 生料量2设定当前值
 * @param unit 单位
 * @param reason 调整原因
 */
void ParameterAdjustmentManager::store_raw_material_set_suggestion_internal(float raw_material1_set_suggested,
                                                                          float raw_material1_set_current,
                                                                          float raw_material2_set_suggested,
                                                                          float raw_material2_set_current,
                                                                          const std::string& unit,
                                                                          const std::string& reason) {
    raw_material_set_suggestion.has_suggestion = true;
    raw_material_set_suggestion.raw_material1_set_suggested = raw_material1_set_suggested;
    raw_material_set_suggestion.raw_material1_set_current = raw_material1_set_current;
    raw_material_set_suggestion.raw_material2_set_suggested = raw_material2_set_suggested;
    raw_material_set_suggestion.raw_material2_set_current = raw_material2_set_current;
    raw_material_set_suggestion.unit = unit;
    raw_material_set_suggestion.reason = reason;
    raw_material_set_suggestion.timestamp = std::chrono::steady_clock::now();

    debug_printf("存储生料量设定调整建议 - 生料量1设定: 当前%.3f%s→建议%.3f%s, 生料量2设定: 当前%.3f%s→建议%.3f%s, 原因: %s\n",
                raw_material1_set_current, unit.c_str(), raw_material1_set_suggested, unit.c_str(),
                raw_material2_set_current, unit.c_str(), raw_material2_set_suggested, unit.c_str(),
                reason.c_str());
}

/**
 * @brief 存储调整建议（供外部调用，带锁保护）
 * @param parameter_name 参数名称
 * @param suggested_value 建议值
 * @param current_value 当前值
 * @param unit 单位
 * @param reason 调整原因
 */
void ParameterAdjustmentManager::store_adjustment_suggestion(const std::string& parameter_name,
                                                           float suggested_value,
                                                           float current_value,
                                                           const std::string& unit,
                                                           const std::string& reason) {
    std::lock_guard<std::mutex> lock(adjustment_mutex);
    store_adjustment_suggestion_internal(parameter_name, suggested_value, current_value, unit, reason);
}

/**
 * @brief 获取当前调整建议
 * @return 当前调整建议结构
 */
ParameterAdjustmentManager::CurrentAdjustmentSuggestion
ParameterAdjustmentManager::get_current_adjustment_suggestion() const {
    std::lock_guard<std::mutex> lock(adjustment_mutex);
    return current_suggestion;
}

/**
 * @brief 获取生料量设定调整建议
 * @return 生料量设定调整建议结构
 */
ParameterAdjustmentManager::RawMaterialSetSuggestion
ParameterAdjustmentManager::get_raw_material_set_suggestion() const {
    std::lock_guard<std::mutex> lock(adjustment_mutex);
    return raw_material_set_suggestion;
}

// 计算调整建议（手动模式）
ParameterAdjustmentManager::AdjustmentSuggestion
ParameterAdjustmentManager::calculate_adjustment_suggestion(float actual_raw_material, float planned_raw_material, bool using_temp_planned) {
    AdjustmentSuggestion suggestion;

    // 统一转换为kg单位进行计算，避免精度丢失
    // 注意：现在所有DCS数据都是t/h单位，需要转换为kg/h进行内部计算
    float actual_raw_material_kg = actual_raw_material * 1000.0f; // t/h转换为kg/h
    float planned_raw_material_kg = planned_raw_material;

    if (using_temp_planned) {
        // 临时计划生料量使用t/h单位，需要转换为kg/h
        planned_raw_material_kg = planned_raw_material * 1000.0f; // t/h转换为kg/h
    } else {
        // DCS计划生料量现在也是t/h单位，需要转换为kg/h
        planned_raw_material_kg = planned_raw_material * 1000.0f; // t/h转换为kg/h
    }

    float diff_kg = actual_raw_material_kg - planned_raw_material_kg;
    float abs_diff_kg = std::abs(diff_kg);

    suggestion.current_diff = diff_kg;
    suggestion.need_adjustment = (abs_diff_kg > raw_material_diff_threshold);

    if (suggestion.need_adjustment) {
        // 计算建议的调整值（统一使用kg单位的调节步长）
        float adjustment_amount_kg;
        if (diff_kg > 0) {
            // 实际生料量大于计划生料量，减少实际生料量
            adjustment_amount_kg = -raw_material_adjustment_step; // 使用kg单位的调节步长
        } else {
            // 实际生料量小于计划生料量，增加实际生料量
            adjustment_amount_kg = raw_material_adjustment_step; // 使用kg单位的调节步长
        }

        // 计算建议值（kg单位）
        float suggested_raw_material_kg = actual_raw_material_kg + adjustment_amount_kg;

        // 转换为返回单位（现在统一返回t/h单位，因为所有DCS数据都是t/h）
        suggestion.suggested_raw_material = suggested_raw_material_kg / 1000.0f; // kg转换为t

        // 确保建议值在合理范围内
        if (suggestion.suggested_raw_material < 0) {
            suggestion.suggested_raw_material = 0;
        }

        if (diff_kg > 0) {
            suggestion.reason = "Actual raw material exceeds planned by " + std::to_string(abs_diff_kg) + "kg, suggest reducing actual amount";
        } else {
            suggestion.reason = "Actual raw material below planned by " + std::to_string(abs_diff_kg) + "kg, suggest increasing actual amount";
        }
    } else {
        suggestion.suggested_raw_material = planned_raw_material;
        suggestion.reason = "Raw material difference within normal range, no adjustment needed";
    }

    return suggestion;
}







// 记录调整操作
void ParameterAdjustmentManager::log_adjustment_action(const std::string& dcs_name,
                                                     float actual_value,
                                                     float old_planned_value,
                                                     float new_planned_value,
                                                     const std::string& reason) {
    debug_printf("参数调整记录 - DCS: %s\n", dcs_name.c_str());
    debug_printf("  实际生料量: %.2fkg\n", actual_value);
    debug_printf("  原计划生料量: %.2fkg\n", old_planned_value);
    debug_printf("  新计划生料量: %.2fkg\n", new_planned_value);
    debug_printf("  调整原因: %s\n", reason.c_str());
    debug_printf("  调整时间: %lld\n",
                std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now().time_since_epoch()).count());
}

// ============================================================================
// C接口函数，供QML调用
// ============================================================================

// 启用/禁用自动调整
extern "C" void enable_auto_parameter_adjustment(bool enable) {
    if (g_parameter_adjustment_manager) {
        g_parameter_adjustment_manager->enable_auto_adjustment(enable);
        debug_printf("C接口: 自动参数调整 %s\n", enable ? "启用" : "禁用");
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
    }
}

// 获取调整建议（手动模式）
extern "C" bool get_adjustment_suggestion(const char* dcs_name,
                                        float* suggested_value,
                                        float* current_diff,
                                        char* reason_buffer,
                                        int buffer_size) {
    if (!g_parameter_adjustment_manager || !dcs_name || !suggested_value || !current_diff) {
        return false;
    }

    // 获取当前存储的调整建议
    auto current_suggestion = g_parameter_adjustment_manager->get_current_adjustment_suggestion();

    if (!current_suggestion.has_suggestion) {
        debug_printf("C接口: 当前没有调整建议\n");
        return false;
    }

    *suggested_value = current_suggestion.suggested_value;
    *current_diff = current_suggestion.suggested_value - current_suggestion.current_value;

    if (reason_buffer && buffer_size > 0) {
        std::string full_reason = current_suggestion.parameter_name + ": " + current_suggestion.reason;
        strncpy(reason_buffer, full_reason.c_str(), buffer_size - 1);
        reason_buffer[buffer_size - 1] = '\0';
    }

    debug_printf("C接口: 获取调整建议 - DCS: %s, 参数: %s, 建议值: %.3f%s, 当前值: %.3f%s\n",
                dcs_name, current_suggestion.parameter_name.c_str(),
                current_suggestion.suggested_value, current_suggestion.unit.c_str(),
                current_suggestion.current_value, current_suggestion.unit.c_str());

    return true;
}

// 获取给煤量调整建议（C接口）
extern "C" bool get_coal_feed_adjustment_suggestion(const char* dcs_name,
                                                  float* suggested_value,
                                                  float* current_value,
                                                  char* reason_buffer,
                                                  int buffer_size) {
    if (!dcs_name || !suggested_value || !current_value) {
        debug_printf("C接口错误: 空指针参数\n");
        return false;
    }

    if (!g_parameter_adjustment_manager) {
        debug_printf("C接口错误: 参数调整管理器未初始化\n");
        return false;
    }

    // 使用OPC设备获取数据
    float current_coal_feed_rate = 0.0f;
    float furnace_set_temp = 0.0f;
    float furnace_actual_temp = 0.0f;

    auto opc_it = dcs_opc_map.find(dcs_name);
    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        std::lock_guard<std::mutex> lock(opc_it->second->rwMutex);
        current_coal_feed_rate = opc_it->second->coal_feed_rate;
        // 如果OPC标签为空，使用临时炉膛设定温度
        if (opc_it->second->opc_furnace_set_temp_tag.empty() || opc_it->second->furnace_set_temp == 0.0f) {
            furnace_set_temp = g_parameter_adjustment_manager->get_temp_furnace_set_temp();
        } else {
            furnace_set_temp = opc_it->second->furnace_set_temp;
        }
        furnace_actual_temp = opc_it->second->furnace_actual_temp;
    } else {
        debug_printf("C接口错误: 无法获取DCS OPC设备 '%s'\n", dcs_name);
        return false;
    }

    *current_value = current_coal_feed_rate;

    // 从配置文件获取炉温相关参数
    float temp_threshold = g_config_manager->get<float>("ParameterAdjustment", "FurnaceTempDiffThreshold", 10.0f);
    float coal_adjustment_step = g_config_manager->get<float>("ParameterAdjustment", "CoalFeedAdjustmentStep", 10.0f);
    float coal_max_value = g_config_manager->get<float>("ParameterAdjustment", "CoalFeedMaxValue", 50.0f);
    float coal_min_value = g_config_manager->get<float>("ParameterAdjustment", "CoalFeedMinValue", 5.0f);

    // 将kg单位的调节步长转换为t/h单位
    float effective_coal_adjustment_step = coal_adjustment_step / 1000.0f;

    // 判断是否需要给煤量调整
    float temp_diff = furnace_actual_temp - furnace_set_temp;
    float abs_temp_diff = std::abs(temp_diff);

    if (abs_temp_diff <= temp_threshold) {
        *suggested_value = current_coal_feed_rate;
        if (reason_buffer && buffer_size > 0) {
            strncpy(reason_buffer, "Furnace temperature within normal range, no coal feed adjustment needed", buffer_size - 1);
            reason_buffer[buffer_size - 1] = '\0';
        }
        return false;
    }

    // 计算建议的给煤量
    if (temp_diff > 0) {
        // 温度过高，减煤
        *suggested_value = current_coal_feed_rate - effective_coal_adjustment_step;
        if (reason_buffer && buffer_size > 0) {
            strncpy(reason_buffer, "Furnace temperature too high, suggest reducing coal feed", buffer_size - 1);
            reason_buffer[buffer_size - 1] = '\0';
        }
    } else {
        // 温度过低，加煤
        *suggested_value = current_coal_feed_rate + effective_coal_adjustment_step;
        if (reason_buffer && buffer_size > 0) {
            strncpy(reason_buffer, "Furnace temperature too low, suggest increasing coal feed", buffer_size - 1);
            reason_buffer[buffer_size - 1] = '\0';
        }
    }

    // 确保建议值在安全范围内
    if (*suggested_value > coal_max_value) {
        *suggested_value = coal_max_value;
    } else if (*suggested_value < coal_min_value) {
        *suggested_value = coal_min_value;
    }

    debug_printf("C接口: 获取给煤量调整建议 - DCS: %s, 当前值: %.3ft/h, 建议值: %.3ft/h\n",
                dcs_name, *current_value, *suggested_value);

    return true;
}





// 获取引风机转速调整建议（C接口）
extern "C" bool get_induced_draft_fan_adjustment_suggestion(const char* dcs_name,
                                                          float* suggested_adjustment,
                                                          float* current_pressure,
                                                          char* reason_buffer,
                                                          int buffer_size) {
    if (!dcs_name || !suggested_adjustment || !current_pressure) {
        debug_printf("C接口错误: 空指针参数\n");
        return false;
    }

    if (!g_parameter_adjustment_manager) {
        debug_printf("C接口错误: 参数调整管理器未初始化\n");
        return false;
    }

    // 使用OPC设备获取数据
    float furnace_pressure = 0.0f;

    auto opc_it = dcs_opc_map.find(dcs_name);
    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        // 检查OPC连接状态
        if (!opc_it->second->is_opc_connected()) {
            debug_printf("C接口: DCS OPC设备 '%s' 未连接，无法获取炉压数据\n", dcs_name);
            return false;
        }

        std::lock_guard<std::mutex> lock(opc_it->second->rwMutex);
        furnace_pressure = opc_it->second->actual_furnace_pressure;
    } else {
        debug_printf("C接口错误: 无法获取DCS OPC设备 '%s'\n", dcs_name);
        return false;
    }

    *current_pressure = furnace_pressure;

    // 从配置文件获取炉压相关参数
    float pressure_setpoint = g_config_manager->get<float>("ParameterAdjustment", "FurnacePressureSetpoint", -75.0f);
    float pressure_threshold = g_config_manager->get<float>("ParameterAdjustment", "FurnacePressureDiffThreshold", 1.0f);
    float fan_adjustment_step = g_config_manager->get<float>("ParameterAdjustment", "InducedDraftFanAdjustmentStep", 0.1f);

    // 判断是否需要引风机转速调整
    float pressure_diff = furnace_pressure - pressure_setpoint;
    float abs_diff = std::abs(pressure_diff);

    if (abs_diff <= pressure_threshold) {
        *suggested_adjustment = 0.0f;
        if (reason_buffer && buffer_size > 0) {
            strncpy(reason_buffer, "Furnace pressure within normal range, no induced draft fan adjustment needed", buffer_size - 1);
            reason_buffer[buffer_size - 1] = '\0';
        }
        return false;
    }

    // 计算建议的引风机转速调整量
    if (pressure_diff > 0) {
        // 炉压过高，减少引风机转速
        *suggested_adjustment = -fan_adjustment_step;
        if (reason_buffer && buffer_size > 0) {
            strncpy(reason_buffer, "Furnace pressure too high, suggest reducing induced draft fan speed", buffer_size - 1);
            reason_buffer[buffer_size - 1] = '\0';
        }
    } else {
        // 炉压过低，增加引风机转速
        *suggested_adjustment = fan_adjustment_step;
        if (reason_buffer && buffer_size > 0) {
            strncpy(reason_buffer, "Furnace pressure too low, suggest increasing induced draft fan speed", buffer_size - 1);
            reason_buffer[buffer_size - 1] = '\0';
        }
    }

    debug_printf("C接口: 获取引风机调整建议 - DCS: %s, 炉压: %.2fPa, 建议调整: %.2frpm\n",
                dcs_name, *current_pressure, *suggested_adjustment);

    return true;
}

// 获取自动调整状态
extern "C" bool is_auto_adjustment_active() {
    if (g_parameter_adjustment_manager) {
        return g_parameter_adjustment_manager->is_auto_adjustment_enabled();
    }
    return false;
}

// ============================================================================
// 各参数独立调节方法实现
// ============================================================================

/**
 * @brief 生料量参数调节方法
 * @param dcs_name DCS设备名称
 * @param actual_raw_material 实际总生料量（实际生料量1+实际生料量2）（t/h）
 * @param planned_raw_material 计划生料量（t/h）
 * @param using_temp_planned 是否使用临时计划生料量
 * @param target_raw_material_set 目标生料量设定值（生料量1设定或生料量2设定）（t/h）
 *
 * 业务逻辑：
 * 1. 使用总生料量与计划生料量进行对比判断是否需要调整
 * 2. 如果需要调整，计算针对目标生料量设定值的调整建议值
 * 3. 调整策略：使用配置的调节步长进行调整，避免过度调整
 * 4. 自动模式：发送生料量设定值的调整值到DCS系统
 * 5. 手动模式：只计算生料量设定值的建议值，不发送DCS指令
 */
void ParameterAdjustmentManager::adjust_raw_material_parameter(const std::string& dcs_name,
                                                             float actual_raw_material,
                                                             float planned_raw_material,
                                                             bool using_temp_planned,
                                                             float target_raw_material_set) {
    // 计算总生料量差值（用于判断是否需要调整）
    // 统一转换为kg单位进行计算，避免精度丢失
    // 注意：现在所有DCS数据都是t/h单位，需要转换为kg/h进行内部计算
    float actual_raw_material_kg = actual_raw_material * 1000.0f; // t/h转换为kg/h
    float planned_raw_material_kg = planned_raw_material;

    if (using_temp_planned) {
        // 临时计划生料量使用t/h单位，需要转换为kg/h
        planned_raw_material_kg = planned_raw_material * 1000.0f; // t/h转换为kg/h
    } else {
        // DCS计划生料量现在也是t/h单位，需要转换为kg/h
        planned_raw_material_kg = planned_raw_material * 1000.0f; // t/h转换为kg/h
    }

    float diff_kg = actual_raw_material_kg - planned_raw_material_kg;
    float abs_diff_kg = std::abs(diff_kg);

    std::string target_name = (raw_material_setpoint_target == 1) ? "生料量1设定" : "生料量2设定";

    if (using_temp_planned) {
        debug_printf("[生料量调节] 检查 - 实际总生料量: %.3fkg/h, 计划: %.3ft/h (%.3fkg/h), 差值: %.3fkg, 阈值: %.1fkg\n",
                    actual_raw_material_kg, planned_raw_material, planned_raw_material_kg, abs_diff_kg, raw_material_diff_threshold);
        debug_printf("[生料量调节] 当前%s: %.3ft/h\n", target_name.c_str(), target_raw_material_set);
    } else {
        debug_printf("[生料量调节] 检查 - 实际总生料量: %.3fkg/h, 计划: %.3fkg/h, 差值: %.3fkg, 阈值: %.1fkg\n",
                    actual_raw_material_kg, planned_raw_material_kg, abs_diff_kg, raw_material_diff_threshold);
        debug_printf("[生料量调节] 当前%s: %.3ft/h\n", target_name.c_str(), target_raw_material_set);
    }

    // 检查是否需要调整（使用kg单位进行比较）
    if (abs_diff_kg <= raw_material_diff_threshold) {
        debug_printf("[生料量调节] 差值在阈值范围内，无需调整\n");
        return;
    }

    debug_printf("[生料量调节] 差值超过阈值，需要调整%s\n", target_name.c_str());

    // 计算针对目标生料量设定值的调整量（使用配置的调节步长，统一用kg单位计算）
    float adjustment_amount_kg;

    if (diff_kg > 0) {
        // 总生料量大于计划生料量，减少目标生料量设定值
        adjustment_amount_kg = -raw_material_adjustment_step; // 使用kg单位的调节步长
    } else {
        // 总生料量小于计划生料量，增加目标生料量设定值
        adjustment_amount_kg = raw_material_adjustment_step; // 使用kg单位的调节步长
    }

    debug_printf("[生料量调节] 调节步长: %.1fkg, 调整量: %.1fkg\n",
                raw_material_adjustment_step, adjustment_amount_kg);

    // 将目标生料量设定值转换为kg单位进行计算
    // 注意：现在所有DCS设定值都是t/h单位，需要转换为kg/h进行内部计算
    float target_raw_material_set_kg = target_raw_material_set * 1000.0f; // t/h转换为kg/h

    float new_raw_material_set_value_kg = target_raw_material_set_kg + adjustment_amount_kg;

    // 确保新值在合理范围内（不能为负数）
    if (new_raw_material_set_value_kg < 0) {
        new_raw_material_set_value_kg = 0;
        debug_printf("[生料量调节] %s调整值被限制为最小值: 0kg/h\n", target_name.c_str());
    }

    std::string reason;
    if (diff_kg > 0) {
        reason = "Total raw material exceeds planned, reducing " + target_name;
    } else {
        reason = "Total raw material below planned, increasing " + target_name;
    }

    // 计算最终返回给QML的值（现在统一返回t/h单位，因为所有DCS数据都是t/h）
    float new_raw_material_set_value_for_qml = new_raw_material_set_value_kg / 1000.0f; // kg/h转换为t/h
    debug_printf("[生料量调节] 建议调整 - 新%s: %.3ft/h (%.3fkg/h), 调整量: %.3fkg (步长: %.1fkg)\n",
                target_name.c_str(), new_raw_material_set_value_for_qml, new_raw_material_set_value_kg,
                adjustment_amount_kg, raw_material_adjustment_step);

    // 同时计算两个生料量设定的调整建议
    // 获取当前的生料量1设定和生料量2设定值（kg/h单位）
    float raw_material1_set_current_kg = 0.0f;
    float raw_material2_set_current_kg = 0.0f;

    auto opc_it = dcs_opc_map.find(dcs_name);
    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        std::lock_guard<std::mutex> dcs_lock(opc_it->second->rwMutex);
        raw_material1_set_current_kg = opc_it->second->raw_material1_set;
        raw_material2_set_current_kg = opc_it->second->raw_material2_set;

        // 现在所有DCS设定值都是t/h单位，需要转换为kg/h进行内部计算
        raw_material1_set_current_kg *= 1000.0f; // t/h转换为kg/h
        raw_material2_set_current_kg *= 1000.0f; // t/h转换为kg/h
    }

    // 计算两个生料量设定的建议值（kg/h单位）
    float raw_material1_set_suggested_kg = raw_material1_set_current_kg + adjustment_amount_kg;
    float raw_material2_set_suggested_kg = raw_material2_set_current_kg + adjustment_amount_kg;

    // 确保建议值不为负数
    if (raw_material1_set_suggested_kg < 0) raw_material1_set_suggested_kg = 0;
    if (raw_material2_set_suggested_kg < 0) raw_material2_set_suggested_kg = 0;

    // 存储生料量设定调整建议（同时存储两个生料量设定的建议）
    // 注意：现在统一使用t/h单位存储，与DCS数据单位保持一致
    std::string unit = "t/h";  // 生料量设定统一使用t/h单位存储
    store_raw_material_set_suggestion_internal(raw_material1_set_suggested_kg / 1000.0f, raw_material1_set_current_kg / 1000.0f,
                                             raw_material2_set_suggested_kg / 1000.0f, raw_material2_set_current_kg / 1000.0f,
                                             unit, reason);

    // 根据用户选择存储当前激活的调整建议（用于兼容现有的QML接口）
    // 存储t/h单位的值，与DCS数据单位保持一致
    store_adjustment_suggestion_internal(target_name, new_raw_material_set_value_for_qml, target_raw_material_set, unit, reason);

    // 根据调整模式执行不同操作
    if (auto_adjustment_enabled) {
        debug_printf("[生料量调节] 自动调整模式 - 发送%s调整指令到DCS\n", target_name.c_str());

        // 发送生料量设定调整指令到DCS
        auto opc_it = dcs_opc_map.find(dcs_name);
        int result = -1;
        if (opc_it != dcs_opc_map.end() && opc_it->second) {
            // 选择正确的写入标签
            std::string write_tag = (raw_material_setpoint_target == 1) ?
                                   opc_it->second->opc_raw_material1_set_write_tag :
                                   opc_it->second->opc_raw_material2_set_write_tag;

            // DCS写入值：现在DCS统一使用t/h单位
            float dcs_write_value = new_raw_material_set_value_for_qml; // 使用t/h单位的值
            result = opc_it->second->write_float_value(write_tag, dcs_write_value);

            debug_printf("[生料量调节] DCS写入值: %.3ft/h\n", dcs_write_value);
        }

        if (result == 0) {
            log_adjustment_action(dcs_name, actual_raw_material, planned_raw_material,
                                new_raw_material_set_value_for_qml, reason);
            debug_printf("[生料量调节] %s调整指令发送成功\n", target_name.c_str());
        } else {
            debug_printf("[生料量调节] %s调整指令发送失败\n", target_name.c_str());
        }
    } else {
        debug_printf("[生料量调节] 手动调整模式 - 仅提供%s建议值，不发送DCS指令\n", target_name.c_str());
        debug_printf("[生料量调节] %s建议值已计算完成，等待用户手动调整\n", target_name.c_str());
    }
}



/**
 * @brief 给煤量参数调节方法，炉膛温度判断（步骤1执行）
 * @param dcs_name DCS设备名称
 * @param furnace_set_temp 炉膛设定温度（℃）
 * @param furnace_actual_temp 炉膛实际温度（℃）
 * @param coal_feed_set 当前给煤量设定值（t/h）
 *
 * 业务逻辑（步骤1）：
 * 1. 判断炉膛实际温度和设定温度的关系
 * 2. 实际温度 > 设定温度：减少给煤量设定（每次减10kg）
 * 3. 实际温度 < 设定温度：增加给煤量设定（每次加10kg）
 * 4. 确保给煤量设定在安全范围内
 * 5. 调整完成后，下次调整周期回到步骤1重新判断炉膛温度
 */
void ParameterAdjustmentManager::adjust_coal_feed_parameter(const std::string& dcs_name,
                                                          float furnace_set_temp,
                                                          float furnace_actual_temp,
                                                          float coal_feed_set) {
    debug_printf("[给煤量调节] 检查 - 设定温度: %.1f℃, 实际温度: %.1f℃\n",
                furnace_set_temp, furnace_actual_temp);
    debug_printf("[给煤量调节] 当前给煤量设定: %.3ft/h\n", coal_feed_set);

    float new_coal_feed_set = coal_feed_set;
    bool need_adjustment = false;
    std::string adjustment_reason;

    // 将kg单位的调节步长转换为t/h单位（与原始版本保持一致）
    float effective_coal_adjustment_step = coal_feed_adjustment_step / 1000.0f;
    debug_printf("[给煤量调节] 调节步长从%.1fkg转换为%.3ft/h\n",
                coal_feed_adjustment_step, effective_coal_adjustment_step);

    if (furnace_actual_temp > furnace_set_temp) {
        // 6.1 炉膛实际温度高于设定温度，减少给煤量设定
        new_coal_feed_set = coal_feed_set - effective_coal_adjustment_step;
        need_adjustment = true;
        adjustment_reason = "Furnace temperature too high, reducing coal feed setpoint";
        debug_printf("[给煤量调节] 炉膛温度过高，减少给煤量设定 %.3ft/h\n", effective_coal_adjustment_step);

    } else if (furnace_actual_temp < furnace_set_temp) {
        // 6.5 炉膛实际温度低于设定温度，增加给煤量设定
        new_coal_feed_set = coal_feed_set + effective_coal_adjustment_step;
        need_adjustment = true;
        adjustment_reason = "Furnace temperature too low, increasing coal feed setpoint";
        debug_printf("[给煤量调节] 炉膛温度过低，增加给煤量设定 %.3ft/h\n", effective_coal_adjustment_step);
    } else {
        debug_printf("[给煤量调节] 炉膛温度已达到设定值，无需调节给煤量\n");
        return;
    }

    // 确保给煤量设定在安全范围内（t/h单位）
    if (new_coal_feed_set > coal_feed_max_value) {
        new_coal_feed_set = coal_feed_max_value;
        debug_printf("[给煤量调节] 给煤量设定被限制为最大值: %.3ft/h\n", coal_feed_max_value);
    } else if (new_coal_feed_set < coal_feed_min_value) {
        new_coal_feed_set = coal_feed_min_value;
        debug_printf("[给煤量调节] 给煤量设定被限制为最小值: %.3ft/h\n", coal_feed_min_value);
    }

    if (!need_adjustment) {
        debug_printf("[给煤量调节] 给煤量设定在正常范围内，无需调整\n");
        return;
    }

    debug_printf("[给煤量调节] 需要调整给煤量设定\n");
    debug_printf("[给煤量调节] 建议调整 - 当前: %.3ft/h → 新值: %.3ft/h (调整量: %.3ft/h)\n",
                coal_feed_set, new_coal_feed_set, new_coal_feed_set - coal_feed_set);

    // 存储调整建议（供QML页面获取）- 使用内部版本，因为外层已加锁
    // 给煤量设定使用t/h单位存储（与原始版本保持一致）
    store_adjustment_suggestion_internal("给煤量设定", new_coal_feed_set, coal_feed_set, "t/h", adjustment_reason);

    // 根据调整模式执行不同操作（本次调整周期只发送一个给煤量设定调整指令）
    if (auto_adjustment_enabled) {
        debug_printf("[给煤量调节] 自动调整模式 - 发送给煤量设定调整指令到DCS\n");

        // 发送给煤量设定调整指令到OPC DCS
        auto opc_it = dcs_opc_map.find(dcs_name);
        int result = -1;
        if (opc_it != dcs_opc_map.end() && opc_it->second) {
            result = opc_it->second->write_float_value(opc_it->second->opc_coal_feed_set_write_tag, new_coal_feed_set);
        }

        if (result == 0) {
            debug_printf("[给煤量调节] 给煤量设定调整指令发送成功，等待下次调整周期重新判断炉膛温度\n");
            log_adjustment_action(dcs_name, coal_feed_set, coal_feed_set,
                                new_coal_feed_set, adjustment_reason);
        } else {
            debug_printf("[给煤量调节] 给煤量设定调整指令发送失败\n");
        }
    } else {
        debug_printf("[给煤量调节] 手动调整模式 - 仅提供建议值，不发送DCS指令\n");
        debug_printf("[给煤量调节] 建议: %s %.3ft/h给煤量设定\n",
                    (new_coal_feed_set > coal_feed_set) ? "增加" : "减少",
                    std::abs(new_coal_feed_set - coal_feed_set));
    }
}



/**
 * @brief 氧气浓度参数调节方法（步骤2执行）
 * @param dcs_name DCS设备名称
 * @param oxygen_concentration 当前氧气浓度（%）
 * @param coal_feed_set 当前给煤量设定值（kg）
 * @param target_raw_material_set 目标生料量设定值（生料量1设定或生料量2设定）（kg）
 *
 * 业务逻辑：
 * 1. 进入此函数时，炉膛温度已经在设定范围内（由步骤1保证）
 * 2. 直接进行氧气浓度调节，不再重新判断炉膛温度
 * 3. 氧气浓度大于设定值：增加生料量设定
 * 4. 氧气浓度小于设定值：减少给煤量设定
 */
void ParameterAdjustmentManager::adjust_oxygen_concentration_parameter(const std::string& dcs_name,
                                                                     float oxygen_concentration,
                                                                     float coal_feed_set,
                                                                     float target_raw_material_set) {
    auto opc_it = dcs_opc_map.find(dcs_name);
    if (opc_it == dcs_opc_map.end() || !opc_it->second) {
        debug_printf("[氧气浓度调节] 错误: 无法获取DCS OPC设备 '%s'\n", dcs_name.c_str());
        return;
    }

    debug_printf("[氧气浓度调节] 开始氧气浓度调节检查\n");
    debug_printf("[氧气浓度调节] 当前氧气浓度: %.2f%%, 设定氧气浓度: %.2f%%\n",
                oxygen_concentration, oxygen_concentration_setpoint);

    float concentration_diff = oxygen_concentration - oxygen_concentration_setpoint;
    float abs_diff = std::abs(concentration_diff);

    // 检查是否需要调整
    if (abs_diff <= oxygen_concentration_diff_threshold) {
        debug_printf("[氧气浓度调节] 氧气浓度在阈值范围内，无需调整\n");
        return;
    }

    std::string adjustment_reason;
    bool adjustment_success = false;

    std::string target_name = (raw_material_setpoint_target == 1) ? "生料量1设定" : "生料量2设定";

    if (concentration_diff > 0) {
        // 氧气浓度大于设定值，增加生料量设定
        debug_printf("[氧气浓度调节] 氧气浓度过高，增加%s %.3fkg\n", target_name.c_str(), oxygen_raw_material_adjustment_step);
        adjustment_reason = "Oxygen concentration too high, increasing " + target_name;

        float new_raw_material_set = target_raw_material_set + oxygen_raw_material_adjustment_step;

        // 获取当前的生料量1设定和生料量2设定值
        float raw_material1_set_current = 0.0f;
        float raw_material2_set_current = 0.0f;
        {
            std::lock_guard<std::mutex> dcs_lock(opc_it->second->rwMutex);
            raw_material1_set_current = opc_it->second->raw_material1_set;
            raw_material2_set_current = opc_it->second->raw_material2_set;
        }

        // 同时计算两个生料量设定的调整建议
        float raw_material1_set_suggested = raw_material1_set_current + oxygen_raw_material_adjustment_step;
        float raw_material2_set_suggested = raw_material2_set_current + oxygen_raw_material_adjustment_step;

        // 存储生料量设定调整建议（同时存储两个生料量设定的建议）
        // 注意：生料量设定统一使用kg单位存储（QML显示时会转换为t/h）
        store_raw_material_set_suggestion_internal(raw_material1_set_suggested, raw_material1_set_current,
                                                 raw_material2_set_suggested, raw_material2_set_current,
                                                 "kg", adjustment_reason);

        // 存储当前激活的调整建议（用于兼容现有的QML接口）
        store_adjustment_suggestion_internal(target_name, new_raw_material_set, target_raw_material_set, "kg", adjustment_reason);

        // 根据调整模式执行不同操作
        if (auto_adjustment_enabled) {
            debug_printf("[氧气浓度调节] 自动调整模式 - 发送%s调整指令到DCS\n", target_name.c_str());

            // 选择正确的写入标签
            std::string write_tag = (raw_material_setpoint_target == 1) ?
                                   opc_it->second->opc_raw_material1_set_write_tag :
                                   opc_it->second->opc_raw_material2_set_write_tag;

            int result = opc_it->second->write_float_value(write_tag, new_raw_material_set);

            if (result == 0) {
                log_adjustment_action(dcs_name, oxygen_concentration, oxygen_concentration_setpoint,
                                    new_raw_material_set, adjustment_reason);
                debug_printf("[氧气浓度调节] %s调整指令发送成功，等待下次调整周期重新判断\n", target_name.c_str());
                adjustment_success = true;
            } else {
                debug_printf("[氧气浓度调节] %s调整指令发送失败\n", target_name.c_str());
            }
        } else {
            debug_printf("[氧气浓度调节] 手动调整模式 - 仅提供建议值，不发送DCS指令\n");
            adjustment_success = true;
        }
    } else {
        // 氧气浓度小于设定值，减少给煤量设定
        // 将kg单位的调节步长转换为t/h单位（与原始版本保持一致）
        float effective_coal_adjustment_step = oxygen_coal_feed_adjustment_step / 1000.0f;
        debug_printf("[氧气浓度调节] 氧气浓度过低，减少给煤量设定 %.3ft/h\n", effective_coal_adjustment_step);
        adjustment_reason = "Oxygen concentration too low, reducing coal feed setpoint";

        float new_coal_feed_set = coal_feed_set - effective_coal_adjustment_step;

        // 确保不低于最小值（t/h单位）
        if (new_coal_feed_set < coal_feed_min_value) {
            new_coal_feed_set = coal_feed_min_value;
            debug_printf("[氧气浓度调节] 给煤量设定被限制为最小值: %.3ft/h\n", coal_feed_min_value);
        }

        // 存储调整建议（供QML页面获取）- 使用内部版本，因为外层已加锁
        // 给煤量设定使用t/h单位存储（与原始版本保持一致）
        store_adjustment_suggestion_internal("给煤量设定", new_coal_feed_set, coal_feed_set, "t/h", adjustment_reason);

        // 根据调整模式执行不同操作
        if (auto_adjustment_enabled) {
            debug_printf("[氧气浓度调节] 自动调整模式 - 发送给煤量设定调整指令到DCS\n");

            int result = opc_it->second->write_float_value(opc_it->second->opc_coal_feed_set_write_tag, new_coal_feed_set);

            if (result == 0) {
                log_adjustment_action(dcs_name, oxygen_concentration, oxygen_concentration_setpoint,
                                    new_coal_feed_set, adjustment_reason);
                debug_printf("[氧气浓度调节] 给煤量设定调整指令发送成功，等待下次调整周期重新判断\n");
                adjustment_success = true;
            } else {
                debug_printf("[氧气浓度调节] 给煤量设定调整指令发送失败\n");
            }
        } else {
            debug_printf("[氧气浓度调节] 手动调整模式 - 仅提供建议值，不发送DCS指令\n");
            adjustment_success = true;
        }
    }
}

/**
 * @brief 引风机参数调节方法,炉压判断（步骤3执行）
 * @param dcs_name DCS设备名称
 * @param actual_furnace_pressure 实际炉压（Pa）
 * @param induced_draft_fan_set 当前引风机转速设定值（rpm）
 *
 * 业务逻辑（步骤3）：
 * 1. 判断实际炉压和给定炉压的关系
 * 2. 实际炉压 > 给定炉压：减少引风机转速设定（幅度0.1rpm）
 * 3. 实际炉压 < 给定炉压：提高引风机转速设定（幅度0.1rpm）
 * 4. 调整完成后，下次调整周期回到步骤0，重新判断总生料量是否达标
 */
void ParameterAdjustmentManager::adjust_induced_draft_fan_parameter(const std::string& dcs_name,
                                                                  float actual_furnace_pressure,
                                                                  float induced_draft_fan_set) {
    debug_printf("[引风机调节] 检查 - 实际炉压: %.1fPa, 给定炉压: %.1fPa\n",
                actual_furnace_pressure, furnace_pressure_setpoint);
    debug_printf("[引风机调节] 当前引风机转速设定: %.1frpm\n", induced_draft_fan_set);

    float pressure_diff = actual_furnace_pressure - furnace_pressure_setpoint;
    float abs_diff = std::abs(pressure_diff);

    // 检查是否需要调整
    if (abs_diff <= furnace_pressure_diff_threshold) {
        debug_printf("[引风机调节] 炉压在阈值范围内，无需调整\n");
        return;
    }

    float new_fan_speed_set = 0.0f;
    std::string adjustment_reason;

    if (pressure_diff > 0) {
        // 7. 实际炉压大于给定炉压，减少引风机转速设定
        new_fan_speed_set = induced_draft_fan_set - induced_draft_fan_adjustment_step;
        adjustment_reason = "Furnace pressure too high, reducing induced draft fan speed setpoint";
        debug_printf("[引风机调节] 实际炉压过高，减少引风机转速设定 %.1frpm\n", induced_draft_fan_adjustment_step);
    } else {
        // 8. 实际炉压小于给定炉压，提高引风机转速设定
        new_fan_speed_set = induced_draft_fan_set + induced_draft_fan_adjustment_step;
        adjustment_reason = "Furnace pressure too low, increasing induced draft fan speed setpoint";
        debug_printf("[引风机调节] 实际炉压过低，提高引风机转速设定 %.1frpm\n", induced_draft_fan_adjustment_step);
    }

    debug_printf("[引风机调节] 建议调整 - 新引风机转速设定: %.1frpm (调节量: %.1frpm)\n",
                new_fan_speed_set, new_fan_speed_set - induced_draft_fan_set);

    // 存储调整建议（供QML页面获取）- 使用内部版本，因为外层已加锁
    store_adjustment_suggestion_internal("引风机转速设定", new_fan_speed_set, induced_draft_fan_set, "rpm", adjustment_reason);

    // 根据调整模式执行不同操作（本次调整周期只发送一个引风机转速设定调整指令）
    if (auto_adjustment_enabled) {
        debug_printf("[引风机调节] 自动调整模式 - 发送引风机转速设定调整指令到DCS\n");

        // 使用OPC设备发送引风机转速设定调整指令到DCS
        int result = -1;
        auto opc_it = dcs_opc_map.find(dcs_name);
        if (opc_it != dcs_opc_map.end() && opc_it->second) {
            result = opc_it->second->write_float_value(opc_it->second->opc_induced_draft_fan_set_write_tag, new_fan_speed_set);
        } else {
            debug_printf("[引风机调节] 错误: 无法获取DCS OPC设备 '%s'\n", dcs_name.c_str());
        }

        if (result == 0) {
            debug_printf("[引风机调节] 引风机转速设定调整指令发送成功，等待下次调整周期重新判断\n");
            log_adjustment_action(dcs_name, actual_furnace_pressure, furnace_pressure_setpoint,
                                new_fan_speed_set, adjustment_reason);
        } else {
            debug_printf("[引风机调节] 引风机转速设定调整指令发送失败\n");
        }
    } else {
        debug_printf("[引风机调节] 手动调整模式 - 仅提供建议值，不发送DCS指令\n");
        debug_printf("[引风机调节] 建议: %s引风机转速设定 %.1frpm\n",
                    (new_fan_speed_set > induced_draft_fan_set) ? "提高" : "降低",
                    std::abs(new_fan_speed_set - induced_draft_fan_set));
    }
}

// ==================== C接口函数 ====================

extern "C" {

/**
 * @brief 设置给定炉压（C接口）
 * @param pressure 给定炉压值（Pa）
 */
void set_furnace_pressure_setpoint(float pressure) {
    if (g_parameter_adjustment_manager) {
        g_parameter_adjustment_manager->set_furnace_pressure_setpoint(pressure);
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
    }
}

/**
 * @brief 设置设定氧气浓度（C接口）
 * @param concentration 设定氧气浓度值（%）
 */
void set_oxygen_concentration_setpoint(float concentration) {
    if (g_parameter_adjustment_manager) {
        g_parameter_adjustment_manager->set_oxygen_concentration_setpoint(concentration);
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
    }
}

/**
 * @brief 获取给定炉压（C接口）
 * @return 给定炉压值（Pa）
 */
float get_furnace_pressure_setpoint() {
    if (g_parameter_adjustment_manager) {
        return g_parameter_adjustment_manager->get_furnace_pressure_setpoint();
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
        return -75.0f; // 返回默认值
    }
}

/**
 * @brief 获取设定氧气浓度（C接口）
 * @return 设定氧气浓度值（%）
 */
float get_oxygen_concentration_setpoint() {
    if (g_parameter_adjustment_manager) {
        return g_parameter_adjustment_manager->get_oxygen_concentration_setpoint();
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
        return 3.5f; // 返回默认值
    }
}

/**
 * @brief 设置临时炉膛设定温度
 * @param temp 临时炉膛设定温度值（℃）
 */
void set_temp_furnace_set_temp(float temp) {
    if (g_parameter_adjustment_manager) {
        g_parameter_adjustment_manager->set_temp_furnace_set_temp(temp);
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
    }
}

/**
 * @brief 设置临时计划生料量
 * @param material 临时计划生料量值（t/h）
 */
void set_temp_planned_raw_material(float material) {
    if (g_parameter_adjustment_manager) {
        g_parameter_adjustment_manager->set_temp_planned_raw_material(material);
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
    }
}

/**
 * @brief 获取临时炉膛设定温度
 * @return 临时炉膛设定温度值（℃）
 */
float get_temp_furnace_set_temp() {
    if (g_parameter_adjustment_manager) {
        return g_parameter_adjustment_manager->get_temp_furnace_set_temp();
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
        return 1000.0f; // 返回默认值
    }
}

/**
 * @brief 获取临时计划生料量
 * @return 临时计划生料量值（t/h）
 */
float get_temp_planned_raw_material() {
    if (g_parameter_adjustment_manager) {
        return g_parameter_adjustment_manager->get_temp_planned_raw_material();
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
        return 5.0f; // 返回默认值
    }
}

/**
 * @brief 设置生料量设定目标选择
 * @param target 生料量设定调整目标（1=生料量1设定，2=生料量2设定）
 */
void set_raw_material_setpoint_target(int target) {
    if (g_parameter_adjustment_manager) {
        g_parameter_adjustment_manager->set_raw_material_setpoint_target(target);
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
    }
}

/**
 * @brief 获取生料量设定目标选择
 * @return 生料量设定调整目标（1=生料量1设定，2=生料量2设定）
 */
int get_raw_material_setpoint_target() {
    if (g_parameter_adjustment_manager) {
        return g_parameter_adjustment_manager->get_raw_material_setpoint_target();
    } else {
        debug_printf("错误: 参数调整管理器未初始化\n");
        return 2; // 返回默认值
    }
}

/**
 * @brief 获取当前DCS设定值数据
 * @param dcs_name DCS设备名称
 * @param coal_feed_set 输出参数：给煤量设定值
 * @param raw_material1_set 输出参数：生料量1设定值
 * @param raw_material2_set 输出参数：生料量2设定值
 * @param induced_draft_fan_set 输出参数：引风机转速设定值
 */
void get_current_dcs_set_values(const char* dcs_name, float* coal_feed_set, float* raw_material1_set, float* raw_material2_set, float* induced_draft_fan_set) {
    if (!dcs_name || !coal_feed_set || !raw_material1_set || !raw_material2_set || !induced_draft_fan_set) {
        debug_printf("错误: get_current_dcs_set_values 参数为空\n");
        return;
    }

    // 调用dcsopc.cpp中的函数获取设定值数据
    get_realtime_dcs_opc_setpoint_data(std::string(dcs_name), coal_feed_set, raw_material1_set, raw_material2_set, induced_draft_fan_set);
}

} // extern "C"
