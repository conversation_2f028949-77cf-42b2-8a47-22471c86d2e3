import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12

Page {
    id: dataScreenPage

    // 设置背景图片
    background: Image {
        source: "qrc:/images/background.png"
        fillMode: Image.PreserveAspectCrop
        cache: true
    }

    // 页面加载完成后启动硬件数据监控
    Component.onCompleted: {
        // 启动数据大屏的硬件数据监控
        dataScreen.setIsRunning(true);

        // 如果有可用的分解炉，设置第一个为默认选择
        if (dataScreen.boilerList.length > 0 && !dataScreen.currentBoiler) {
            dataScreen.setCurrentBoiler(dataScreen.boilerList[0]);
        }
    }

    // 页面销毁时停止监控
    Component.onDestruction: {
        dataScreen.setIsRunning(false);
    }

    // 监听硬件数据变化
    Connections {
        target: dataScreen
        function onDataChanged() {
            // 静默处理数据更新
        }
    }

    // 监听分解炉列表变化
    Connections {
        target: dataScreen
        function onBoilerListChanged() {
            // 如果当前没有选择分解炉且有可用分解炉，自动选择第一个
            if (!dataScreen.currentBoiler && dataScreen.boilerList.length > 0) {
                dataScreen.setCurrentBoiler(dataScreen.boilerList[0]);
            }
        }
    }

    // 全屏容器
    Item {
        anchors.fill: parent

        // 顶部系统标题背景 - 最上面居中
        Image {
            id: topSystemTitle
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 20
            source: "qrc:/images/top-bg.png"
            fillMode: Image.PreserveAspectFit
            cache: true
        }

        // 系统标题文字
        Text {
            anchors.centerIn: topSystemTitle
            text: "多维智慧燃烧控制系统"
            color: "white"
            font.pixelSize: 30
            font.bold: true
        }

        // 分解炉标题背景图片 - 换行居左
        Image {
            id: titleBackground
            anchors.left: parent.left
            anchors.leftMargin: 40
            anchors.top: topSystemTitle.bottom
            anchors.topMargin: -20
            source: "qrc:/images/title-bg.png"
            fillMode: Image.PreserveAspectFit
            cache: true
        }

        // 分解炉标题文字
        Text {
            anchors.centerIn: titleBackground
            text: "分解炉"
            color: "white"
            font.pixelSize: 24
            font.bold: true
        }

        // 返回首页按钮
        Item {
            id: backHomeButton
            width: 169
            height: 53
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.top: parent.top
            anchors.topMargin: 35
            z: 10

            Image {
                id: backHomeImage
                anchors.fill: parent
                // anchors.topMargin: 25   // 减小上边距，让区域往上移
                source: "qrc:/images/backhome.png"
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    // 返回首页
                    stackView.pop();
                }
            }
        }

        // 左侧框架 - 基于title-bg.png换行居左
        Image {
            id: leftFrame
            anchors.left: parent.left
            anchors.leftMargin: 40  // 与title-bg.png保持相同的左边距
            anchors.top: titleBackground.bottom
            anchors.topMargin: 15   // 减小上边距，让区域往上移
            width: parent.width * 0.15   // 减小宽度从0.25到0.15
            height: parent.height * 0.8// 设置高度为屏幕高度的70%
            source: "qrc:/images/left-frame.png"
            fillMode: Image.Stretch  // 改为拉伸模式，保持设定的宽高
            cache: true
        }

        // 左侧参数显示区域
        Column {
            id: leftParametersColumn
            anchors.left: leftFrame.left
            anchors.leftMargin: 30  // 稍微增加内边距以保持美观
            anchors.top: leftFrame.top
            anchors.topMargin: 60
            width: leftFrame.width - 60  // 相应调整宽度
            spacing: 35

            // 温度参数
            Row {
                spacing: 15
                Image {
                    id: temperatureIcon
                    width: 100
                    height: 100
                    source: "qrc:/images/temperature-icon.png"
                    fillMode: Image.PreserveAspectFit
                }
                Column {
                    anchors.verticalCenter: temperatureIcon.verticalCenter
                    Text {
                        text: "温度"
                        color: "white"
                        font.pixelSize: 16
                        font.bold: true
                    }
                    Text {
                        text: dataScreen.furnaceActualTemp || "0°C"
                        color: "#FF6C6C"
                        font.pixelSize: 20
                        font.bold: true
                    }
                }
            }

            // 负压参数
            Row {
                spacing: 15
                Image {
                    id: pressureIcon
                    width: 100
                    height: 100
                    source: "qrc:/images/pressure-icon.png"
                    fillMode: Image.PreserveAspectFit
                }
                Column {
                    anchors.verticalCenter: pressureIcon.verticalCenter
                    Text {
                        text: "负压"
                        color: "white"
                        font.pixelSize: 16
                        font.bold: true
                    }
                    Text {
                        text: dataScreen.actualFurnacePressure || "0Pa"
                        color: "#75C1F0"
                        font.pixelSize: 20
                        font.bold: true
                    }
                }
            }

            // 氧浓度参数
            Row {
                spacing: 15
                Image {
                    id: oxygenIcon
                    width: 100
                    height: 100
                    source: "qrc:/images/oxygen-icon.png"
                    fillMode: Image.PreserveAspectFit
                }
                Column {
                    anchors.verticalCenter: oxygenIcon.verticalCenter
                    Text {
                        text: "氧浓度"
                        color: "white"
                        font.pixelSize: 16
                        font.bold: true
                    }
                    Text {
                        text: dataScreen.oxygenContent || "0%"
                        color: "#69D9CA"
                        font.pixelSize: 20
                        font.bold: true
                    }
                }
            }

            // 一氧化碳浓度参数
            Row {
                spacing: 15
                Image {
                    id: coIcon
                    width: 100
                    height: 100
                    source: "qrc:/images/co-icon.png"
                    fillMode: Image.PreserveAspectFit
                }
                Column {
                    anchors.verticalCenter: coIcon.verticalCenter
                    Text {
                        text: "一氧化碳浓度"
                        color: "white"
                        font.pixelSize: 16
                        font.bold: true
                    }
                    Text {
                        text: dataScreen.coContent || "0ppm"
                        color: "#DFC541"
                        font.pixelSize: 20
                        font.bold: true
                    }
                }
            }

            // 引风机转速参数
            Row {
                spacing: 15
                Image {
                    id: fanIcon
                    width: 100
                    height: 100
                    source: "qrc:/images/fan-icon.png"
                    fillMode: Image.PreserveAspectFit
                }
                Column {
                    anchors.verticalCenter: fanIcon.verticalCenter
                    Text {
                        text: "引风机转速"
                        color: "white"
                        font.pixelSize: 16
                        font.bold: true
                    }
                    Text {
                        text: dataScreen.inducedDraftFanSpeed || "0rpm"
                        color: "#6D8FFF"
                        font.pixelSize: 20
                        font.bold: true
                    }
                }
            }
        }

        // 右侧大框架 - 基于左侧框架定位
        Image {
            id: rightFrame
            anchors.left: leftFrame.right
            anchors.leftMargin: 5   // 进一步减小与左侧框架的间距
            anchors.right: parent.right
            anchors.rightMargin: 15  // 距离右边界保持小距离
            anchors.top: titleBackground.bottom
            anchors.topMargin: 15   // 减小上边距，让区域往上移
            height: parent.height * 0.8
            // width: parent.width * 0.85   // 使用anchors.right来自动计算宽度，更好地填充空间
            source: "qrc:/images/right-frame.png"
            fillMode: Image.PreserveAspectFit
            cache: true
        }

        // 右侧主图
        Image {
            id: mainDiagram
            anchors.centerIn: rightFrame
            width: rightFrame.width * 0.85
            height: rightFrame.height * 0.85
            source: "qrc:/images/main-diagram.png"
            fillMode: Image.PreserveAspectFit
            cache: true
        }
    }
}
