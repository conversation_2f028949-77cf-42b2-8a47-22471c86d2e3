#include "stdafx.h"

typedef void  (WINAPI * DATACHANGEPROC)(DWORD, DWORD, DWORD, VARIANT, FILETIME , WORD);
typedef BOOL  (WINAPI * ASDAC_SETDATACHANGEPROC)(DWORD, DAT<PERSON>HANGEPROC);
typedef void  (WIN<PERSON><PERSON> * SHUTDOWNPROC)(DWORD);
typedef BOOL  (WINAPI * ASDAC_SETSHUTDOWNPROC)(DWORD, SHUTDOWNPROC);

typedef DWORD (WINAPI * ASDAC_GETVERSION)();
typedef BOOL  (WINAPI * ASDAC_INIT)();
typedef BOOL  (WINAPI * ASDAC_UNINIT)();
typedef BOOL  (WIN<PERSON>I * ASDAC_ACTIVECODE)(LPCSTR,LPCSTR);

typedef DWORD (WINAPI * ASDAC_GETSERVERS)(LPCSTR , DWORD, VARIANT*, VARIANT*);
typedef DWORD (WINAPI * ASDAC_CONNECT)(LPCSTR, LPCSTR, DWORD);
typedef BOOL  (WINAPI * ASDAC_DISCONNECT)(DWORD);
typedef BOOL  (WINAPI * ASDAC_GETSERVERSTATUS)(DWORD, _FILETIME*, _FILETIME*, _FILETIME*, WORD*, DWORD*, DWORD*, WORD*, WORD*, WORD*, LPCSTR, DWORD);
typedef DWORD (WINAPI * ASDAC_ADDGROUP)(DWORD, LPCSTR, BOOL, DWORD, DWORD,float, DWORD);
typedef BOOL  (WINAPI * ASDAC_REMOVEGROUP)(DWORD,DWORD);
typedef BOOL  (WINAPI * ASDAC_SETGROUPNAME)(DWORD,DWORD,LPCSTR);
typedef BOOL  (WINAPI * ASDAC_SETGROUPSTAT)(DWORD,DWORD,DWORD,BOOL,DWORD, float,DWORD);
typedef BOOL  (WINAPI * ASDAC_GETGROUPSTAT)(DWORD,DWORD,DWORD*,BOOL*,DWORD*,float*,DWORD*);
typedef BOOL  (WINAPI * ASDAC_REFRESHGROUP)(DWORD,DWORD,DWORD);

typedef DWORD (WINAPI * ASDAC_ADDITEM)(DWORD ,DWORD ,LPCSTR ); 
typedef BOOL  (WINAPI * ASDAC_REMOVEITEM)(DWORD ,DWORD , DWORD );
typedef BOOL  (WINAPI * ASDAC_WRITEITEM)(DWORD ,DWORD  ,DWORD , VARIANT , BOOL);
typedef BOOL  (WINAPI * ASDAC_READITEM)(DWORD ,DWORD ,DWORD , VARIANT* , _FILETIME* ,WORD* ); 
typedef BOOL  (WINAPI * ASDAC_ACTIVEITEM)(DWORD , DWORD , DWORD, BOOL );
typedef BOOL  (WINAPI * ASDAC_VALIDATEITEM)(DWORD ,LPCSTR ,WORD , DWORD);

typedef BOOL  (WINAPI * ASDAC_GETNAMESPACE)(DWORD , WORD* );
typedef BOOL  (WINAPI * ASDAC_CHANGEBROWSEPOSITION)(DWORD ,int,  LPCSTR );
typedef DWORD (WINAPI * ASDAC_BROWSEITEMS)(DWORD ,WORD, LPCSTR, WORD, WORD, VARIANT*); 
typedef BOOL  (WINAPI * ASDAC_GETITEMFULLNAME)(DWORD , LPCSTR , LPCSTR , DWORD ); 
typedef DWORD (WINAPI * ASDAC_GETITEMPROPERTIES)(DWORD ,LPCSTR , VARIANT*, VARIANT*, VARIANT*);
typedef BOOL  (WINAPI * ASDAC_GETITEMPROPERTYVALUE)(DWORD ,LPCSTR ,DWORD ,VARIANT* ); 

extern ASDAC_SETDATACHANGEPROC ASDAC_SetDataChangeProc;
extern ASDAC_SETSHUTDOWNPROC ASDAC_SetShutdownProc;

extern ASDAC_GETVERSION ASDAC_GetVersion;
extern ASDAC_INIT ASDAC_Init;
extern ASDAC_UNINIT ASDAC_Uninit;
extern ASDAC_GETSERVERS ASDAC_GetServers;
extern ASDAC_CONNECT ASDAC_Connect;
extern ASDAC_DISCONNECT ASDAC_Disconnect;
extern ASDAC_GETSERVERSTATUS ASDAC_GetServerStatus;

extern ASDAC_ADDGROUP ASDAC_AddGroup;
extern ASDAC_REMOVEGROUP ASDAC_RemoveGroup;
extern ASDAC_SETGROUPNAME ASDAC_SetGroupName;
extern ASDAC_SETGROUPSTAT ASDAC_SetGroupStat; 
extern ASDAC_GETGROUPSTAT ASDAC_GetGroupStat; 
extern ASDAC_REFRESHGROUP ASDAC_RefreshGroup;

extern ASDAC_ADDITEM ASDAC_AddItem;
extern ASDAC_REMOVEITEM ASDAC_RemoveItem; 
extern ASDAC_WRITEITEM ASDAC_WriteItem;
extern ASDAC_READITEM ASDAC_ReadItem;
extern ASDAC_ACTIVEITEM ASDAC_ActiveItem;
extern ASDAC_VALIDATEITEM ASDAC_ValidateItem;

extern ASDAC_GETNAMESPACE ASDAC_GetNameSpace;
extern ASDAC_CHANGEBROWSEPOSITION ASDAC_ChangeBrowsePosition;
extern ASDAC_BROWSEITEMS ASDAC_BrowseItems;
extern ASDAC_GETITEMFULLNAME ASDAC_GetItemFullName;
extern ASDAC_GETITEMPROPERTIES ASDAC_GetItemProperties;
extern ASDAC_GETITEMPROPERTYVALUE ASDAC_GetItemPropertyValue;

extern BOOL InitOPCDef();
extern BOOL FreeOPCDef();