#include "DACLTSDK.h"
#include <iostream>

// 全局变量定义
HMODULE hLibrary = NULL;

// 函数指针定义 - 按照官方示例的方式
ASDAC_SETDATACHANGEPROC ASDAC_SetDataChangeProc;
ASDAC_SETSHUTDOWNPROC ASDAC_SetShutdownProc;
ASDAC_GETVERSION ASDAC_GetVersion;
ASDAC_INIT ASDAC_Init;
ASDAC_UNINIT ASDAC_Uninit;
ASDAC_ACTIVECODE ASDAC_ActiveCode;
ASDAC_GETSERVERS ASDAC_GetServers;
ASDAC_CONNECT ASDAC_Connect;
ASDAC_DISCONNECT ASDAC_Disconnect;
ASDAC_GETSERVERSTATUS ASDAC_GetServerStatus;

ASDAC_ADDGROUP ASDAC_AddGroup;
ASDAC_REMOVEGROUP ASDAC_RemoveGroup;
ASDAC_SETGROUPNAME ASDAC_SetGroupName;
ASDAC_SETGROUPSTAT ASDAC_SetGroupStat;
ASDAC_GETGROUPSTAT ASDAC_GetGroupStat;
ASDAC_REFRESHGROUP ASDAC_RefreshGroup;

ASDAC_ADDITEM ASDAC_AddItem;
ASDAC_REMOVEITEM ASDAC_RemoveItem;
ASDAC_WRITEITEM ASDAC_WriteItem;
ASDAC_READITEM ASDAC_ReadItem;
ASDAC_ACTIVEITEM ASDAC_ActiveItem;
ASDAC_VALIDATEITEM ASDAC_ValidateItem;

ASDAC_GETNAMESPACE ASDAC_GetNameSpace;
ASDAC_CHANGEBROWSEPOSITION ASDAC_ChangeBrowsePosition;
ASDAC_BROWSEITEMS ASDAC_BrowseItems;
ASDAC_GETITEMFULLNAME ASDAC_GetItemFullName;
ASDAC_GETITEMPROPERTIES ASDAC_GetItemProperties;
ASDAC_GETITEMPROPERTYVALUE ASDAC_GetItemPropertyValue;

BOOL InitOPCDef()
{
    // 加载DACLTSDK.DLL - 按照官方示例的方式
    hLibrary = LoadLibrary(TEXT("DACLTSDK.DLL"));
    if(hLibrary)
    {
        // 获取函数地址 - 完全按照官方示例的格式
        ASDAC_SetDataChangeProc=(ASDAC_SETDATACHANGEPROC)GetProcAddress(hLibrary,"ASDAC_SetDataChangeProc");
        ASDAC_SetShutdownProc=(ASDAC_SETSHUTDOWNPROC)GetProcAddress(hLibrary,"ASDAC_SetShutdownProc");

        ASDAC_GetVersion=(ASDAC_GETVERSION )GetProcAddress(hLibrary,"ASDAC_GetVersion");
        ASDAC_Init=(ASDAC_INIT )GetProcAddress(hLibrary,"ASDAC_Init");
        ASDAC_Uninit=(ASDAC_UNINIT )GetProcAddress(hLibrary,"ASDAC_Uninit");
        ASDAC_ActiveCode=(ASDAC_ACTIVECODE )GetProcAddress(hLibrary,"ASDAC_ActiveCode");
        ASDAC_GetServers=(ASDAC_GETSERVERS )GetProcAddress(hLibrary,"ASDAC_GetServers");
        ASDAC_Connect=(ASDAC_CONNECT )GetProcAddress(hLibrary,"ASDAC_Connect");
        ASDAC_Disconnect=(ASDAC_DISCONNECT )GetProcAddress(hLibrary,"ASDAC_Disconnect");
        ASDAC_GetServerStatus=(ASDAC_GETSERVERSTATUS )GetProcAddress(hLibrary,"ASDAC_GetServerStatus");

        ASDAC_AddGroup=(ASDAC_ADDGROUP )GetProcAddress(hLibrary,"ASDAC_AddGroup");
        ASDAC_RemoveGroup=(ASDAC_REMOVEGROUP )GetProcAddress(hLibrary,"ASDAC_RemoveGroup");
        ASDAC_SetGroupName=(ASDAC_SETGROUPNAME )GetProcAddress(hLibrary,"ASDAC_SetGroupName");
        ASDAC_SetGroupStat=(ASDAC_SETGROUPSTAT )GetProcAddress(hLibrary,"ASDAC_SetGroupStat");
        ASDAC_GetGroupStat=(ASDAC_GETGROUPSTAT )GetProcAddress(hLibrary,"ASDAC_GetGroupStat");
        ASDAC_RefreshGroup=(ASDAC_REFRESHGROUP )GetProcAddress(hLibrary,"ASDAC_RefreshGroup");

        ASDAC_AddItem=(ASDAC_ADDITEM )GetProcAddress(hLibrary,"ASDAC_AddItem");
        ASDAC_RemoveItem=(ASDAC_REMOVEITEM )GetProcAddress(hLibrary,"ASDAC_RemoveItem");
        ASDAC_WriteItem=(ASDAC_WRITEITEM )GetProcAddress(hLibrary,"ASDAC_WriteItem");
        ASDAC_ReadItem=(ASDAC_READITEM )GetProcAddress(hLibrary,"ASDAC_ReadItem");
        ASDAC_ActiveItem=(ASDAC_ACTIVEITEM )GetProcAddress(hLibrary,"ASDAC_ActiveItem");
        ASDAC_ValidateItem=(ASDAC_VALIDATEITEM )GetProcAddress(hLibrary,"ASDAC_ValidateItem");

        ASDAC_GetNameSpace=(ASDAC_GETNAMESPACE )GetProcAddress(hLibrary,"ASDAC_GetNameSpace");
        ASDAC_ChangeBrowsePosition = (ASDAC_CHANGEBROWSEPOSITION)GetProcAddress(hLibrary,"ASDAC_ChangeBrowsePosition");
        ASDAC_BrowseItems=(ASDAC_BROWSEITEMS )GetProcAddress(hLibrary,"ASDAC_BrowseItems");
        ASDAC_GetItemFullName=(ASDAC_GETITEMFULLNAME )GetProcAddress(hLibrary,"ASDAC_GetItemFullName");
        ASDAC_GetItemProperties=(ASDAC_GETITEMPROPERTIES )GetProcAddress(hLibrary,"ASDAC_GetItemProperties");
        ASDAC_GetItemPropertyValue=(ASDAC_GETITEMPROPERTYVALUE )GetProcAddress(hLibrary,"ASDAC_GetItemPropertyValue");

        // 32位程序稳定性：检查关键函数指针是否成功获取
        if (!ASDAC_Init || !ASDAC_Uninit || !ASDAC_Connect || !ASDAC_Disconnect ||
            !ASDAC_AddGroup || !ASDAC_RemoveGroup || !ASDAC_AddItem || !ASDAC_RemoveItem ||
            !ASDAC_ReadItem || !ASDAC_WriteItem) {
            std::cout << "DACLTSDK.DLL 关键函数指针获取失败" << std::endl;
            FreeLibrary(hLibrary);
            hLibrary = NULL;
            return false;
        }

        std::cout << "DACLTSDK.DLL 加载成功" << std::endl;
        return true;
    }
    else
    {
        std::cout << "无法加载 DACLTSDK.DLL" << std::endl;
        return false;
    }
}

BOOL FreeOPCDef()
{
    if(hLibrary)
        return FreeLibrary(hLibrary);
    else
        return FALSE;
}
