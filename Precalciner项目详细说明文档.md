# Precalciner 分解炉DCS数据采集与参数调整系统

## 项目概述

Precalciner是一个专为水泥厂分解炉设计的智能化DCS数据采集与参数调整系统。该系统集成了烟气分析仪数据采集、DCS系统通讯、智能参数调整算法和可视化监控界面，实现了分解炉燃烧过程的全面监控和自动化优化。

### 主要功能
- **双源数据采集**：RS485烟气分析仪 + DCS系统数据
- **双协议通讯**：烟气分析仪Modbus RTU + DCS系统OPC DA协议
- **智能参数调整**：基于业务逻辑的自动/手动调整算法
- **实时监控界面**：Qt6 + QML现代化用户界面
- **数据持久化**：CSV文件自动记录和历史数据查询
- **多线程架构**：高性能并发数据采集和处理

## 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                    Precalciner 系统架构                          │
├─────────────────────────────────────────────────────────────────┤
│  用户界面层 (QML)                                                │
│  ├── 主界面 (main.qml)                                          │
│  ├── 监控系统 (MonitoringSystem.qml)                            │
│  ├── 数据大屏 (DataScreenView.qml)                              │
│  ├── 参数调整 (ParameterAdjustmentView.qml)                     │
│  └── 配置管理 (CollectionConfigView.qml)                        │
├─────────────────────────────────────────────────────────────────┤
│  数据源层 (C++)                                                  │
│  ├── MonitoringDataSource (monitoring_datasource.cpp) - 监控系统数据源 │
│  ├── DataScreen (datascreen.cpp) - 大屏数据源                   │
│  └── ParameterAdjustmentQML - 参数调整接口                      │
├─────────────────────────────────────────────────────────────────┤
│  业务逻辑层 (C++)                                                │
│  ├── ParameterAdjustmentManager - 参数调整管理器                │
│  ├── ConfigManager - 配置管理器                                 │
│  └── CSVReader - 历史数据读取                                   │
├─────────────────────────────────────────────────────────────────┤
│  数据采集层 (C++)                                                │
│  ├── Boiler - 烟气分析仪采集 (RS485/Modbus RTU)                 │
│  └── DCSOPCDevice - DCS OPC采集 (OPC DA)                        │
├─────────────────────────────────────────────────────────────────┤
│  通讯协议层                                                      │
│  ├── Modbus RTU (smoke_analyzer_comm.cpp)                       │
│  └── OPC DA (dcsopc.cpp)                                        │
├─────────────────────────────────────────────────────────────────┤
│  数据存储层                                                      │
│  ├── CSV文件管理 (csvfile.cpp)                                  │
│  ├── 配置文件 (config.ini)                                      │
│  └── 历史数据目录 (data/)                                       │
└─────────────────────────────────────────────────────────────────┘
```

### 模块关系图
```
main.cpp (程序入口)
    ↓ 初始化
├── ConfigManager ← config.ini
├── Boiler线程池 ← RS485/Modbus RTU协议 (烟气分析仪)
├── DCSOPCDevice线程池 ← OPC DA协议 (DCS系统)
├── ParameterAdjustmentManager ← 调整算法
└── QML界面引擎
    ├── MonitoringDataSource ← 监控系统实时数据
    ├── DataScreen ← DCS数据
    └── CSVReader ← 历史数据
```

## 文件结构分析

### 核心模块文件

#### 程序入口和配置
- **main.cpp** - 程序入口，初始化所有模块和线程
- **config.ini** - 系统配置文件，包含设备参数和调整算法配置
- **config_manager.cpp/.h** - 配置文件解析器，支持INI格式读写

#### 数据采集模块
- **smoke_analyzer_comm.cpp/.h** - RS485串口通讯和Modbus协议实现（烟气分析仪通讯）
- **boiler.cpp/.h** - 烟气分析仪设备管理和数据采集线程
- **dcs.cpp/.h** - DCS Modbus设备管理和双线程采集
- **dcsopc.cpp/.h** - DCS OPC DA设备管理和双线程采集

#### 参数调整模块
- **parameter_adjustment.cpp/.h** - 参数调整算法核心实现
- **parameter_adjustment_qml.cpp/.h** - QML接口封装

#### 用户界面模块
- **monitoring_datasource.cpp/.h** - 监控系统数据源管理，专门为MonitoringSystem.qml提供实时数据
- **datascreen.cpp/.h** - 数据大屏专用数据源
- **monitorwindow.cpp/.h** - 监控窗口管理

#### 数据存储模块
- **csvfile.cpp/.h** - CSV文件写入，支持缓冲和定时刷新
- **csvreader.cpp/.h** - CSV文件读取，支持历史数据查询

#### QML界面文件
- **main.qml** - 主界面入口和导航
- **MonitoringSystem.qml** - 分解炉监控主界面
- **DataScreenView.qml** - 全屏数据大屏
- **ParameterAdjustmentView.qml** - 参数调整界面
- **CollectionConfigView.qml** - 采集配置界面

### 文件依赖关系

```
main.cpp
├── config_manager.h
├── smoke_analyzer_comm.h (→ boiler.h)
├── dcs.h
├── dcsopc.h
├── parameter_adjustment.h
├── monitoring_datasource.h
├── datascreen.h
├── csvreader.h
└── QML文件 (qml.qrc)

数据流向：
config.ini → ConfigManager → 各设备类 → 采集线程 → 数据源类 → QML界面
                                    ↓
                              CSV文件 ← csvfile.h
```

## 核心模块详解

### 1. 数据采集模块

#### 烟气分析仪采集 (Boiler类)
```cpp
class Boiler {
    // 配置参数
    std::string boiler_name;
    int collection_interval;
    
    // 设备地址配置
    unsigned short device_nox, device_co, device_o2, device_so2;
    unsigned short device_current, device_voltage, device_tempature;
    
    // 实时数据
    float co, o2, nox, so2, current, voltage, temperature;
    
    // 线程安全
    std::mutex rwMutex;
    
    // 核心方法
    void start_data_collect();      // 启动采集线程
    void do_data_collect();         // 采集线程函数
    int read_data();                // 数据读取
};
```

**特点：**
- 独立线程持续采集
- 多参数并发读取（CO、O2、NOx、SO2、温度、电压、电流）
- 自动重试机制
- 线程安全的数据访问

#### DCS系统采集 (DCSOPCDevice类)
```cpp
class DCSOPCDevice {
    // 分解炉DCS参数
    float furnace_set_temp;           // 炉膛设定温度
    float furnace_actual_temp;        // 炉膛实际温度
    float actual_furnace_pressure;    // 实际炉压
    float coal_feed_rate;             // 给煤量
    float actual_raw_material;        // 实际生料量
    float planned_raw_material;       // 计划生料量
    float induced_draft_fan_frequency; // 引风机频率

    // 双线程采集
    void start_data_collect();        // 主要参数采集线程
    void start_raw_material_collect(); // 生料量独立采集线程
};
```

**双线程采集架构：**
- **主要参数线程**：采集炉膛温度、压力、给煤量等（20秒间隔）
- **生料量线程**：独立采集生料量参数（20秒间隔）
- **双协议支持**：Modbus RTU + OPC DA可配置切换

## DCS OPC设备全局映射表 (dcs_opc_map) 详解

### 概述
`dcs_opc_map` 是系统的核心数据管理容器，负责存储和管理所有DCS OPC设备实例，为整个系统提供统一的DCS数据访问接口。

### 数据结构定义
```cpp
// 全局变量定义 (dcsopc.cpp)
std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

// 外部声明 (dcsopc.h)
extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;
```

**数据结构说明：**
- **类型**: `std::unordered_map<std::string, DCSOPCDevice*>`
- **键 (Key)**: DCS设备名称 (如 "DCS1", "DCS2")
- **值 (Value)**: DCSOPCDevice对象指针，包含实时数据和OPC连接

### 初始化流程
```cpp
// main.cpp 中的初始化过程
int main() {
    // 1. 创建所有DCS OPC设备列表
    debug_printf("主程序: 开始创建DCS OPC设备列表\n");
    dcs_opc_map = get_dcs_opc_list(&config);  // 从config.ini读取配置并创建设备
    debug_printf("主程序: DCS OPC设备列表创建完成，数量: %zu\n", dcs_opc_map.size());

    // 2. 启动所有设备的采集线程
    for (const auto& pair : dcs_opc_map) {
        pair.second->start_data_collect();        // 启动主要参数采集线程
        pair.second->start_raw_material_collect(); // 启动生料量独立采集线程
    }
}
```

### DCSOPCDevice对象数据存储
每个DCSOPCDevice对象存储完整的DCS实时数据：
```cpp
class DCSOPCDevice {
public:
    // 分解炉DCS参数
    float furnace_set_temp;           // 炉膛设定温度 (°C)
    float furnace_actual_temp;        // 炉膛实际温度 (°C)
    float actual_furnace_pressure;    // 实际炉压 (Pa)
    float coal_feed_rate;             // 给煤量 (kg/h)
    float actual_raw_material;        // 实际生料量 (kg/h)
    float planned_raw_material;       // 计划生料量 (kg/h)
    float induced_draft_fan_frequency; // 引风机频率 (Hz)

    // 线程安全保护
    std::mutex rwMutex;               // 读写互斥锁

    // OPC连接管理
    bool is_opc_connected();          // 检查连接状态
    int write_float_value(tag, value); // 写入控制指令
};
```

### 数据更新机制
```cpp
// DCSOPCDevice::read_data() - OPC采集线程定期调用
int DCSOPCDevice::read_data() {
    // 1. 从OPC服务器读取最新数据
    // ... OPC读取过程 ...

    // 2. 加锁更新设备对象中的数据
    {
        std::lock_guard<std::mutex> lock(rwMutex);
        furnace_set_temp = temp_furnace_set_temp;
        furnace_actual_temp = temp_furnace_actual_temp;
        actual_furnace_pressure = temp_actual_furnace_pressure;
        coal_feed_rate = temp_coal_feed_rate;
        actual_raw_material = temp_actual_raw_material;
        planned_raw_material = temp_planned_raw_material;
        induced_draft_fan_frequency = temp_induced_draft_fan_frequency;
    }

    return success_count > 0 ? 1 : 0;
}
```

**数据流向：**
```
DCS系统 → OPC服务器 → OPC采集线程 → DCSOPCDevice对象 → dcs_opc_map → 各业务模块
```

### 全局访问模式
系统中各模块通过以下方式访问dcs_opc_map：

```cpp
// 1. 声明外部变量
extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

// 2. 查找指定DCS设备
auto opc_it = dcs_opc_map.find("DCS1");
if (opc_it != dcs_opc_map.end() && opc_it->second) {
    DCSOPCDevice* dcs_device = opc_it->second;

    // 3. 线程安全的数据访问
    {
        std::lock_guard<std::mutex> lock(dcs_device->rwMutex);
        float temp = dcs_device->furnace_actual_temp;
        float pressure = dcs_device->actual_furnace_pressure;
    }
}
```

### 使用该映射表的模块
- **parameter_adjustment.cpp**: 参数调整算法，读取当前值并发送控制指令
- **parameter_adjustment_qml.cpp**: QML接口，为界面提供实时数据
- **datascreen.cpp**: 数据大屏，显示DCS设备列表和状态
- **configmanager_qml.cpp**: 配置管理，设备配置更新时重新初始化
- **main.cpp**: 主程序，负责初始化和启动采集线程

### 线程安全机制
- **读写锁保护**: 每个DCSOPCDevice对象都有独立的rwMutex
- **原子操作**: 数据读写都在锁保护下进行
- **多线程访问**: 支持多个模块同时安全访问不同设备

## 数据获取方式详解

### 概述
系统中不同模块根据使用场景采用两种不同的数据获取方式：
- **直接访问模式**：通过全局映射表直接访问设备对象
- **函数接口模式**：通过封装函数获取数据值

### 1. DCS数据获取方式

#### 方式一：直接访问 `dcs_opc_map`（用于控制模块）
基于上述dcs_opc_map全局映射表的直接访问模式：

```cpp
// 实际应用示例：parameter_adjustment_qml.cpp
QVariantMap ParameterAdjustmentQML::getCurrentDCSValues(const QString& dcsName) {
    QVariantMap values;
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

    auto opc_it = dcs_opc_map.find(dcsName.toStdString());
    if (opc_it != dcs_opc_map.end() && opc_it->second) {
        DCSOPCDevice* dcs_opc_device = opc_it->second;

        // 线程安全的数据读取
        {
            std::lock_guard<std::mutex> lock(dcs_opc_device->rwMutex);
            values["actualRawMaterial"] = dcs_opc_device->actual_raw_material;
            values["coalFeedRate"] = dcs_opc_device->coal_feed_rate;
            values["inducedDraftFanFrequency"] = dcs_opc_device->induced_draft_fan_frequency;
        }
    }
    return values;
}
```

**适用场景：**
- 参数调整模块（parameter_adjustment.cpp、parameter_adjustment_qml.cpp）
- 需要设备控制能力的模块
- 需要访问设备状态和方法的场景
- 需要高性能数据访问的场景

#### 方式二：函数接口 `get_realtime_dcs_opc_data()`（用于特定显示模块）
```cpp
// 使用场景：monitoring_datasource.cpp 等特定显示模块
void get_realtime_dcs_opc_data(std::string dcs_name,
                              float *furnace_set_temp,
                              float *furnace_actual_temp,
                              float *actual_furnace_pressure,
                              float *coal_feed_rate,
                              float *actual_raw_material,
                              float *planned_raw_material,
                              float *induced_draft_fan_frequency);

// 调用示例
float furnace_set_temp, furnace_actual_temp, actual_furnace_pressure;
float coal_feed_rate, actual_raw_material, planned_raw_material;
float induced_draft_fan_frequency;

get_realtime_dcs_opc_data(dcsName, &furnace_set_temp, &furnace_actual_temp,
                         &actual_furnace_pressure, &coal_feed_rate,
                         &actual_raw_material, &planned_raw_material,
                         &induced_draft_fan_frequency);
```

**适用场景：**
- 监控界面（monitoring_datasource.cpp）
- 只需要数据值的显示模块
- 跨模块数据访问
- **注意**：datascreen.cpp 已改为直接访问模式以提升性能

### 2. 烟气分析仪数据获取方式

#### 方式一：直接访问 `boiler_map`（用于控制模块）
```cpp
// 使用场景：parameter_adjustment.cpp 获取氧气浓度
extern std::unordered_map<std::string, Boiler*> boiler_map;

auto boiler_it = boiler_map.find(boiler_name);
if (boiler_it != boiler_map.end() && boiler_it->second) {
    Boiler* boiler = boiler_it->second;

    // 读取数据（需要加锁）
    {
        std::lock_guard<std::mutex> lock(boiler->rwMutex);
        float oxygen_concentration = boiler->o2;  // 氧气浓度
        float co_concentration = boiler->co;      // 一氧化碳浓度
        // ... 其他参数
    }

    // 检查设备状态
    bool connected = (boiler->fd >= 0);
}
```

#### 方式二：函数接口 `get_realtime_data()`（用于显示模块）
```cpp
// 使用场景：datascreen.cpp 等显示模块
void get_realtime_data(std::string boiler_name,
                      float *pco, float *po2, float *pnox, float *pso2,
                      float *pcurrent, float *pvoltage, float *ptemperature,
                      int *pswitch1);

// 调用示例
float co, o2, nox, so2, current, voltage, temperature;
int switch1;

get_realtime_data(deviceName, &co, &o2, &nox, &so2,
                 &current, &voltage, &temperature, &switch1);
```

### 3. 各模块数据获取方式总览

| 模块 | DCS数据获取 | 烟气数据获取 | 原因 |
|------|-------------|--------------|------|
| **parameter_adjustment.cpp** | 直接访问 `dcs_opc_map` | 直接访问 `boiler_map` | 需要设备控制和状态检查 |
| **datascreen.cpp** | 函数接口 `get_realtime_dcs_opc_data()` | 函数接口 `get_realtime_data()` | 只需要数据值，跨模块访问 |
| **monitoring_datasource.cpp** | 函数接口 `get_realtime_dcs_opc_data()` | 函数接口 `get_realtime_data()` | UI显示，接口稳定性 |

### 4. 设计原则

#### 直接访问模式的优势
- **性能高**：无函数调用开销
- **功能完整**：可访问设备对象的所有属性和方法
- **控制能力**：可以写入控制指令
- **状态检查**：可以检查连接状态、初始化状态等

#### 函数接口模式的优势
- **封装性好**：隐藏内部实现细节
- **接口稳定**：内部实现可以改变而不影响调用者
- **线程安全**：函数内部保证数据访问的线程安全
- **简化调用**：一次调用获取所有需要的数据

#### 选择原则
- **控制模块**：使用直接访问模式（如参数调整）
- **高性能显示模块**：使用直接访问模式（如数据大屏 - 已优化）
- **一般显示模块**：使用函数接口模式（如监控界面）
- **内部访问**：同模块内使用直接访问
- **外部访问**：跨模块根据性能需求选择
- **高频访问**：直接访问性能更好，避免重复查找
- **偶尔访问**：函数接口封装性更好

### 2. 通讯协议模块

#### Modbus RTU协议 (data.cpp)
```cpp
// 核心函数
int modbus_request(int fd, unsigned char slave_addr, unsigned char func_code,
                   unsigned short start_addr, unsigned short quantity, 
                   unsigned char *response);

int read_holding_registers(int fd, unsigned char slave_addr,
                          unsigned short start_addr, unsigned short quantity,
                          unsigned int *registers, bool is32bit);
```

**特点：**
- 支持多设备地址
- IEEE 754浮点数解析
- CRC校验验证
- 自动重试和错误处理

#### OPC DA协议 (dcsopc.cpp)
```cpp
class DCSOPCDevice {
    // OPC接口
    IOPCServer* opc_server;
    IOPCGroupStateMgt* opc_group;
    IOPCItemMgt* opc_item_mgt;
    IOPCSyncIO* opc_sync_io;
    
    // 核心方法
    int connect_opc_server();         // 连接OPC服务器
    int read_opc_item();              // 读取OPC项
    int write_opc_item();             // 写入OPC项
};
```

**特点：**
- 标准OPC DA 2.0协议
- 自动重连机制
- 标签化数据访问
- Windows COM技术

### 3. 参数调整模块

#### 调整算法核心 (ParameterAdjustmentManager)
```cpp
class ParameterAdjustmentManager {
    // 调整流程控制
    void check_and_adjust_parameters(const std::string& dcs_name,
                                   float actual_raw_material,
                                   float planned_raw_material);
    
    // 分层调整逻辑
    void adjust_raw_material();       // 步骤0：生料量调整
    void adjust_coal_feed();          // 步骤1：给煤量调整
    void adjust_oxygen_concentration(); // 步骤2：氧气浓度调整（生料量+煤量）
    void adjust_induced_draft_fan();  // 步骤3：引风机调整
};
```

**业务逻辑：**
```
步骤0：生料量调整 (前置条件)
   ↓ (差值≤10kg时)
步骤1：炉膛温度判断 → 给煤量调节
   ↓ (温度差值≤10°C时)
步骤2：氧气浓度判断 → 生料量/煤量调节
   ├─ 氧气浓度 > 设定值：增加生料量
   └─ 氧气浓度 < 设定值：减少煤量
   ↓ (浓度差值≤0.1%时)
步骤3：炉压判断 → 引风机频率调节
   ↓ (压力差值≤1Pa时)
系统达产
```

**调整模式：**
- **自动模式**：计算建议值并发送DCS指令
- **手动模式**：仅显示建议值，用户手动调整

### 4. 用户界面模块

#### 监控系统数据源管理 (MonitoringDataSource类)
```cpp
class MonitoringDataSource : public QObject {
    Q_OBJECT
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)
    Q_PROPERTY(QString smokeData READ smokeData NOTIFY smokeDataChanged)

public slots:
    void startMonitoring();
    void stopMonitoring();
    void updateData();

signals:
    void smokeDataChanged();
    void chartDataUpdated();
};
```

**特点：**
- Qt属性绑定系统
- 实时数据更新通知
- 多设备数据源切换
- 图表数据格式化
- **专门服务于MonitoringSystem.qml**：职责明确，避免与其他数据源混淆

#### QML界面组件
- **响应式设计**：支持1920x1080全屏显示
- **实时图表**：基于Qt Charts的动态数据可视化
- **参数调整界面**：显示当前值和建议值对比
- **历史数据查询**：按日期查询和导出功能

### 5. 数据存储模块

#### CSV文件管理 (csvfile.cpp)
```cpp
typedef struct {
    FILE *file;                    // 文件指针
    char filename[128];            // 当前文件名
    time_t start_time;             // 文件创建时间
    size_t current_size;           // 当前文件大小（字节）
    size_t max_size;               // 文件最大大小（字节）,0不生效
    char buffer[8192];             // 写入缓冲区
    size_t flush_threshold;        // 刷新阈值
    int flush_interval;            // 刷新间隔
    char fileprefix[128];          // 文件名前缀
} csv_manager_t;
```

**特点：**
- 缓冲写入机制
- 按日期自动分文件
- UTF-8编码支持
- 定时刷新保证数据安全

**数据格式：**
```csv
时间戳,O2,CO,NOx,SO2,测点温度,电压,电流,炉膛设定温度,炉膛实际温度,实际炉压,给煤量,实际生料量,计划生料量,引风机频率
```

#### CSV文件按日期管理机制详解

##### 1. 文件命名规则
系统每天自动生成一个新的CSV文件，文件名格式为：`锅炉名称-YYYYMMDD.csv`

```cpp
// 格式化文件名（按日期）
struct tm *timeinfo = localtime(&now);
char today[60] = {0};
strftime(today, sizeof(today), "%Y%m%d.csv", timeinfo);

// 最终文件名格式：锅炉名称-YYYYMMDD.csv
snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s-%s", exe_dir,
         "\\", "\\", csv->fileprefix, today);
```

**文件名示例：**
- `分解炉1号-20250716.csv` (2025年7月16日)
- `分解炉1号-20250717.csv` (2025年7月17日)

##### 2. 自动日期检测和文件切换
```cpp
// 检查是否需要创建新的日期文件
int need_new_date_file(csv_manager_t *csv) {
    time_t now = time(NULL);
    struct tm *current_tm = localtime(&now);
    struct tm *start_tm = localtime(&csv->start_time);

    // 检查年、月、日是否相同
    if (current_tm->tm_year != start_tm->tm_year ||
        current_tm->tm_mon != start_tm->tm_mon ||
        current_tm->tm_mday != start_tm->tm_mday) {
        debug_printf("检测到日期变化，需要创建新的CSV文件\n");
        return 1;  // 需要新文件
    }
    return 0;  // 不需要新文件
}
```

**工作流程：**
1. 每次写入数据时检查当前日期
2. 如果日期变化（年、月、日任一变化）：
   - 关闭当前文件
   - 创建新的日期文件
   - 写入UTF-8 BOM头
   - 继续数据写入

**时间点示例：**
```
2025-07-16 23:59:50 → 写入 分解炉1号-20250716.csv
2025-07-17 00:00:10 → 自动切换到 分解炉1号-20250717.csv
```

##### 3. 文件存储结构
```
程序目录/
├── data/
│   ├── 分解炉1号-20250714.csv
│   ├── 分解炉1号-20250715.csv
│   ├── 分解炉1号-20250716.csv  ← 今天的文件
│   └── 分解炉1号-20250717.csv  ← 明天自动创建
```

## DCS OPC设备与CSV文件管理的关联机制详解

### 概述
dcsopc.cpp（DCS OPC数据采集）与csvfile.cpp（CSV文件管理）通过3个核心函数实现数据的统一存储管理。DCS设备负责采集烟气分析仪和DCS系统的数据，并统一写入按日期管理的CSV文件中。

### 调用关系流程

#### 1. 初始化阶段 - `init_csv_manager()`
```cpp
void DCSOPCDevice::do_data_collect() {
    // 初始化CSV文件管理器，用于数据记录
    csv_manager_t csv;

    // 使用锅炉名称作为CSV文件前缀
    init_csv_manager(&csv, 0, 4096, 5, this->associated_boiler.c_str());
}
```

**参数说明：**
- `&csv`: CSV管理器结构体指针
- `0`: 文件最大大小（0表示不限制）
- `4096`: 缓冲区刷新阈值（4KB）
- `5`: 刷新间隔（5秒）
- `this->associated_boiler.c_str()`: 文件名前缀（锅炉名称，如"分解炉1号"）

#### 2. 数据写入阶段 - `write_to_csv_buffer()`
```cpp
// 格式化合并数据行（烟气分析仪 + DCS数据）
char combined_data_line[1024];
snprintf(combined_data_line, sizeof(combined_data_line),
        "%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f",
        boiler_o2, boiler_co, boiler_nox, boiler_so2, boiler_temperature, boiler_voltage, boiler_current,
        furnace_set_temp, furnace_actual_temp, actual_furnace_pressure, coal_feed_rate, actual_raw_material, planned_raw_material);

// 将格式化的数据写入CSV缓冲区
// CSV管理器会自动处理文件创建、时间戳添加和缓冲区管理
write_to_csv_buffer(&csv, (const unsigned char*)combined_data_line, strlen(combined_data_line));
```

**功能：**
- 将13个参数的数据格式化为CSV行
- 写入到CSV缓冲区
- 自动处理时间戳添加和日期文件切换

#### 3. 清理阶段 - `close_csv_manager()`
```cpp
// 线程退出时的清理工作
close_csv_manager(&csv);  // 关闭CSV文件管理器，确保数据完整写入
```

**功能：**
- 刷新缓冲区中的剩余数据
- 关闭文件句柄
- 确保数据完整写入磁盘

### 数据统一管理机制

#### 数据采集分工
- **boiler.cpp (烟气分析仪)**：只负责数据采集，**不再写入CSV**
- **dcsopc.cpp (DCS OPC设备)**：负责数据采集 + **统一CSV写入**

#### 数据合并写入流程
```cpp
// 获取关联锅炉的烟气分析仪数据
// 注意：烟气分析仪数据仅用于CSV记录，不会发送给DCS系统
extern std::unordered_map<std::string, Boiler*> boiler_map;
auto boiler_it = boiler_map.find(this->associated_boiler);
if (boiler_it != boiler_map.end() && boiler_it->second) {
    Boiler* boiler = boiler_it->second;

    // 使用锅炉的互斥锁保护数据读取，确保数据一致性
    std::lock_guard<std::mutex> boiler_lock(boiler->rwMutex);
    boiler_co = boiler->co;              // 一氧化碳浓度 (ppm)
    boiler_o2 = boiler->o2;              // 氧气浓度 (%)
    // ... 其他烟气参数
}
```

#### CSV数据格式（13个参数）
```csv
时间戳,O2,CO,NOx,SO2,测点温度,电压,电流,炉膛设定温度,炉膛实际温度,实际炉压,给煤量,实际生料量,计划生料量
```

**数据来源分布：**
- **烟气分析仪数据（7个）**：O2, CO, NOx, SO2, 测点温度, 电压, 电流
- **DCS系统数据（6个）**：炉膛设定温度, 炉膛实际温度, 实际炉压, 给煤量, 实际生料量, 计划生料量

### 自动化功能特性

通过这3个函数调用，dcsopc.cpp获得了以下自动化功能：

#### 1. 自动文件管理
- 自动创建按日期命名的文件
- 自动检测日期变化并切换文件
- 自动创建data目录

#### 2. 缓冲机制
- 4KB缓冲区提高写入性能
- 5秒定时刷新确保数据安全
- 程序退出时强制刷新

#### 3. 数据格式化
- 自动添加时间戳
- UTF-8编码支持
- 标准CSV格式

### 完整数据流向图
```
DCS OPC数据 + 烟气分析仪数据
    ↓ (格式化为CSV行)
write_to_csv_buffer()
    ↓ (检查日期/大小)
create_new_csv_file() (如需要)
    ↓ (写入缓冲区)
定时/手动 flush_csv_buffer()
    ↓ (写入磁盘)
分解炉1号-20250716.csv
```

### 设计优势
1. **数据一致性**：同一时间点的烟气和DCS数据记录在同一行
2. **避免重复**：不会产生多个CSV文件
3. **便于分析**：所有相关数据都在一个文件中，便于后续分析
4. **时间同步**：基于DCS采集时间作为统一时间戳
5. **模块分离**：dcsopc.cpp只需关注数据采集，文件管理由csvfile.cpp自动处理

### 6. 配置管理模块

#### 配置文件结构 (config.ini)
```ini
[ProtocolList]
list=RS485,DCS_RS485

[BoilerList]
list=分解炉1号

[DCSList]
list=DCS1

[DCS1]
CommunicationType = OPC  # Modbus 或 OPC
CollectionInterval = 20
RawMaterialCollectionInterval = 20

[ParameterAdjustment]
AutoAdjustmentEnabled = false
RawMaterialAdjustmentCheckInterval = 3
OtherParametersAdjustmentCheckInterval = 3
```

## 数据流程

### 完整数据流程图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   烟气分析仪     │    │   DCS系统       │    │   参数调整算法   │
│   (RS485)      │    │ (Modbus/OPC)    │    │                │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    数据采集线程池                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │Boiler线程   │  │DCS主要参数  │  │DCS生料量线程            │  │
│  │(15秒间隔)   │  │线程(20秒)   │  │(20秒) → 参数调整检查    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────┬───────────────┬─────────────────────┬─────────────────┘
          │               │                     │
          ▼               ▼                     ▼
┌─────────────────────────────────────────────────────────────────┐
│                    内存数据存储                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │烟气分析仪   │  │DCS实时数据  │  │参数调整建议值           │  │
│  │数据缓存     │  │缓存         │  │缓存                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────┬───────────────┬─────────────────────┬─────────────────┘
          │               │                     │
          ▼               ▼                     ▼
┌─────────────────────────────────────────────────────────────────┐
│                    数据输出层                                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │CSV文件记录  │  │QML界面显示  │  │DCS控制指令发送          │  │
│  │(持久化)     │  │(实时监控)   │  │(自动调整模式)           │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 数据采集到显示流程
1. **数据采集**：多线程并发采集烟气分析仪和DCS数据
2. **数据缓存**：使用互斥锁保护的内存缓存
3. **数据融合**：将烟气分析仪和DCS数据合并
4. **界面更新**：通过Qt信号槽机制更新QML界面
5. **数据记录**：异步写入CSV文件进行持久化

### 参数调整流程
1. **数据触发**：生料量采集线程触发调整检查
2. **算法计算**：基于业务逻辑计算调整建议
3. **模式判断**：根据自动/手动模式决定执行方式
4. **指令发送**：自动模式下发送控制指令到DCS
5. **结果反馈**：更新界面显示和记录调整日志

## 线程架构

### 多线程设计
```
主线程 (main.cpp)
├── QML界面线程 (Qt主线程)
├── 烟气分析仪采集线程池
│   └── Boiler::do_data_collect() × N个设备
├── DCS Modbus采集线程池  
│   ├── DCSDevice::do_data_collect() × N个设备
│   └── DCSDevice::do_raw_material_collect() × N个设备
├── DCS OPC采集线程池
│   ├── DCSOPCDevice::do_data_collect() × N个设备
│   └── DCSOPCDevice::do_raw_material_collect() × N个设备
└── 参数调整线程 (由生料量采集触发)
```

### 线程间通讯机制
- **数据共享**：使用std::mutex保护的共享内存
- **信号通知**：Qt信号槽机制用于界面更新
- **线程同步**：std::lock_guard确保数据一致性
- **线程生命周期**：使用detach()方式，线程独立运行

### 线程安全保证
```cpp
// 数据读写保护
{
    std::lock_guard<std::mutex> lock(rwMutex);
    // 安全访问共享数据
    furnace_temp = device->furnace_actual_temp;
}

// Qt信号槽线程安全
emit dataChanged();  // 自动线程安全的界面更新
```

## 技术特点

### 1. 双协议通讯支持
- **Modbus RTU**：传统工业协议，稳定可靠
- **OPC DA**：现代工业标准，配置简单
- **配置切换**：通过config.ini无缝切换协议

### 2. 双频采集机制
- **主要参数**：20秒间隔采集炉膛温度、压力等
- **生料量参数**：独立20秒间隔，触发参数调整
- **烟气分析仪**：15秒间隔采集环保数据

### 3. 智能参数调整算法
- **分层控制**：生料量→温度→氧浓度→压力的递进式调整
- **业务逻辑**：基于水泥厂实际工艺的调整策略
- **双模式**：自动调整和手动建议模式

### 4. 现代化界面技术
- **Qt6 + QML**：现代化响应式界面
- **实时图表**：基于Qt Charts的动态数据可视化
- **多屏适配**：支持不同分辨率和全屏显示
- **数据源职责分离**：MonitoringDataSource专门服务监控系统，DataScreen服务数据大屏，职责清晰

### 5. 高性能数据处理
- **多线程并发**：充分利用多核CPU性能
- **内存缓存**：减少磁盘I/O，提高响应速度
- **缓冲写入**：批量写入CSV文件，提高效率

### 6. 可靠性设计
- **自动重连**：网络中断后自动恢复连接
- **数据校验**：CRC校验确保数据完整性
- **异常处理**：完善的错误处理和日志记录
- **配置热加载**：支持运行时配置更新

## 数据获取实际代码示例

### 1. 参数调整模块的数据获取（parameter_adjustment.cpp）

#### DCS数据获取和控制
```cpp
void ParameterAdjustmentManager::check_and_adjust_parameters(const std::string& dcs_name,
                                                           float actual_raw_material,
                                                           float planned_raw_material) {
    // 获取DCS OPC设备对象
    auto opc_it = dcs_opc_map.find(dcs_name);
    if (opc_it == dcs_opc_map.end() || !opc_it->second) {
        debug_printf("错误: 无法获取DCS OPC设备 '%s' 的数据\n", dcs_name.c_str());
        return;
    }

    DCSOPCDevice* dcs_opc_device = opc_it->second;
    float furnace_set_temp, furnace_actual_temp, actual_furnace_pressure, coal_feed_rate;

    // 读取DCS数据（加锁保护）
    {
        std::lock_guard<std::mutex> dcs_lock(dcs_opc_device->rwMutex);
        furnace_set_temp = dcs_opc_device->furnace_set_temp;
        furnace_actual_temp = dcs_opc_device->furnace_actual_temp;
        actual_furnace_pressure = dcs_opc_device->actual_furnace_pressure;
        coal_feed_rate = dcs_opc_device->coal_feed_rate;
    }

    // 写入控制指令到DCS
    int result = dcs_opc_device->write_float_value(
        dcs_opc_device->opc_coal_feed_rate_write_tag, new_coal_feed_rate);
}
```

#### 烟气分析仪数据获取
```cpp
// 获取氧气浓度数据（从关联的烟气分析仪获取）
float oxygen_concentration = 0.0f;
extern std::unordered_map<std::string, Boiler*> boiler_map;
auto boiler_it = boiler_map.find(dcs_opc_device->associated_boiler);
if (boiler_it != boiler_map.end() && boiler_it->second) {
    std::lock_guard<std::mutex> boiler_lock(boiler_it->second->rwMutex);
    oxygen_concentration = boiler_it->second->o2;  // 从烟气分析仪获取氧气浓度
    debug_printf("从烟气分析仪获取氧气浓度: %.2f%%\n", oxygen_concentration);
} else {
    debug_printf("警告: 无法获取关联烟气分析仪数据，氧气浓度设为0\n");
}
```

### 2. 数据大屏的数据获取（datascreen.cpp）

#### DCS数据获取
```cpp
void DataScreen::updateDcsData()
{
    // 检查是否有选择的DCS设备
    if (m_currentDcs.isEmpty()) {
        debug_printf("数据大屏: 没有选择DCS设备，设置默认值\n");
        return;
    }

    // 从硬件获取真实DCS数据 - 分解炉参数
    float furnace_set_temp = 0.0f, furnace_actual_temp = 0.0f, actual_furnace_pressure = 0.0f;
    float coal_feed_rate = 0.0f, actual_raw_material = 0.0f, planned_raw_material = 0.0f;
    float induced_draft_fan_frequency = 0.0f;
    std::string dcsName = m_currentDcs.toStdString();

    // 通过函数接口获取DCS数据
    try {
        get_realtime_dcs_opc_data(dcsName, &furnace_set_temp, &furnace_actual_temp,
                                 &actual_furnace_pressure, &coal_feed_rate,
                                 &actual_raw_material, &planned_raw_material,
                                 &induced_draft_fan_frequency);

        // 更新字符串格式的DCS数据（用于大屏显示）
        m_payload = QString::number(coal_feed_rate, 'f', 1) + "t/h";
        m_mainSteamTemp = QString::number(furnace_actual_temp, 'f', 1) + "℃";
        // ... 其他数据格式化
    } catch (...) {
        debug_printf("数据大屏DCS数据获取异常: %s\n", dcsName.c_str());
    }
}
```

#### 烟气分析仪数据获取
```cpp
void DataScreen::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        debug_printf("数据大屏: 没有选择锅炉，跳过数据更新\n");
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f, nox = 0.0f, so2 = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    int switch1 = 0;  // 开关量信号
    std::string deviceName = m_currentBoiler.toStdString();

    // 通过函数接口获取烟气数据
    try {
        get_realtime_data(deviceName, &co, &o2, &nox, &so2,
                         &current, &voltage, &temperature, &switch1);

        // 更新烟气数据显示
        m_noxContent = QString::number(nox, 'f', 0) + "mg/Nm³";
        m_so2Content = QString::number(so2, 'f', 0) + "ppm";
        // ... 其他数据格式化
    } catch (...) {
        debug_printf("数据大屏烟气数据获取异常: %s\n", deviceName.c_str());
    }
}
```

### 3. 数据获取方式选择指南

#### 何时使用直接访问模式
```cpp
// ✅ 适用场景：需要设备控制能力
auto opc_it = dcs_opc_map.find(dcs_name);
if (opc_it != dcs_opc_map.end() && opc_it->second) {
    // 可以读取数据
    float temp = opc_it->second->furnace_actual_temp;

    // 可以写入控制指令
    opc_it->second->write_float_value(tag, value);

    // 可以检查设备状态
    bool connected = opc_it->second->is_opc_connected();
}
```

#### 何时使用函数接口模式
```cpp
// ✅ 适用场景：只需要数据值，跨模块访问
float temp, pressure, rate;
get_realtime_dcs_opc_data(dcs_name, &temp, &pressure, &rate, ...);

// 数据已经通过函数参数返回，无需关心内部实现
display_temperature(temp);
display_pressure(pressure);
```

## DataScreenView.qml 数据大屏页面详解

### 概述
DataScreenView.qml是系统的核心数据展示页面，实时显示分解炉的关键运行参数。该页面通过datascreen.cpp后端模块获取烟气分析仪和DCS系统的实时数据，为操作人员提供直观的监控界面。

### 页面架构
```
DataScreenView.qml (QML界面) ← dataScreen (C++对象) ← datascreen.cpp (数据获取逻辑)
                                        ↓
                                定时器驱动数据更新
                                        ↓
                            updateSmokeData() + updateDcsData()
                                        ↓
                        烟气分析仪数据 + DCS OPC数据
```

### 显示的核心参数
数据大屏显示5个关键参数，分别来自不同的数据源：

#### 来自DCS系统的参数 (3个)
```qml
// 1. 炉膛实际温度
Text {
    text: dataScreen.furnaceActualTemp || "0°C"
    color: "#FF6C6C"  // 红色显示
}

// 2. 实际炉膛压力 (负压)
Text {
    text: dataScreen.actualFurnacePressure || "0Pa"
    color: "#75C1F0"  // 蓝色显示
}

// 3. 引风机频率
Text {
    text: dataScreen.inducedDraftFanFrequency || "0Hz"
    color: "#DFC541"  // 黄色显示
}
```

#### 来自烟气分析仪的参数 (2个)
```qml
// 4. 氧气浓度
Text {
    text: dataScreen.oxygenContent || "0%"
    color: "#69D9CA"  // 青色显示
}

// 5. 一氧化碳浓度
Text {
    text: dataScreen.coContent || "0ppm"
    color: "#DFC541"  // 黄色显示
}
```

### 数据更新机制

#### 1. 页面生命周期管理
```qml
// 页面加载完成后启动硬件数据监控
Component.onCompleted: {
    console.log("DataScreenView component completed loading");
    dataScreen.setIsRunning(true);  // 启动定时器

    // 设置默认分解炉
    if (dataScreen.boilerList.length > 0 && !dataScreen.currentBoiler) {
        dataScreen.setCurrentBoiler(dataScreen.boilerList[0]);
    }
}

// 页面销毁时停止监控
Component.onDestruction: {
    console.log("DataScreenView component destroyed, stopping hardware monitoring");
    dataScreen.setIsRunning(false);  // 停止定时器
}
```

#### 2. 数据变化监听
```qml
// 监听硬件数据变化
Connections {
    target: dataScreen
    function onDataChanged() {
        console.log("DataScreen received hardware data update");
        console.log("Current data values - O2:", dataScreen.oxygenContent, "CO:", dataScreen.coContent);
    }
}
```

### 后端数据获取逻辑 (datascreen.cpp)

#### 定时器驱动的数据更新
```cpp
void DataScreen::updateData() {
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("数据大屏更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    updateSmokeData();    // 更新烟气分析仪数据
    updateDcsData();      // 更新DCS数据

    lastUpdateTime = currentTime;
}
```

#### 烟气分析仪数据获取 (已优化为直接访问模式)
```cpp
void DataScreen::updateSmokeData() {
    // 1. 检查设备连接状态
    std::string deviceName = m_currentBoiler.toStdString();
    auto it = boiler_map.find(deviceName);

    if (it != boiler_map.end() && it->second && it->second->fd >= 0) {
        // 2. 直接从设备对象获取数据，避免重复查找boiler_map
        Boiler* boiler_device = it->second;
        try {
            // 使用互斥锁保护数据读取，确保数据一致性
            {
                std::lock_guard<std::mutex> lock(boiler_device->rwMutex);
                // 只获取数据大屏需要的2个核心参数
                o2 = boiler_device->o2;
                co = boiler_device->co;
            }

            // 3. 更新界面显示数据
            m_oxygenContent = QString::number(o2, 'f', 2) + "%";
            m_coContent = QString::number(co, 'f', 0) + "ppm";

            debug_printf("数据大屏烟气数据直接获取: %s - CO=%.2f, O2=%.2f\n",
                        deviceName.c_str(), co, o2);
        } catch (...) {
            // 如果数据访问出现异常，设置为无效数据
            debug_printf("数据大屏烟气数据直接获取异常: %s\n", deviceName.c_str());
            o2 = co = 0.0f;
            m_oxygenContent = "0.00%";
            m_coContent = "0ppm";
        }
    } else {
        // 设备未连接时显示默认值
        m_oxygenContent = "0.00%";
        m_coContent = "0ppm";
    }

    emit dataChanged();  // 通知QML界面更新
}
```

#### DCS数据获取 (已优化为直接访问模式)
```cpp
void DataScreen::updateDcsData() {
    // 1. 检查DCS OPC连接状态
    std::string dcsName = m_currentDcs.toStdString();
    extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

    auto opc_it = dcs_opc_map.find(dcsName);
    if (opc_it != dcs_opc_map.end() && opc_it->second && opc_it->second->is_initialized) {
        // 2. 直接从设备对象获取数据，避免重复查找dcs_opc_map
        DCSOPCDevice* dcs_device = opc_it->second;
        try {
            // 使用互斥锁保护数据读取，确保数据一致性
            {
                std::lock_guard<std::mutex> lock(dcs_device->rwMutex);
                // 只获取数据大屏需要的3个核心参数
                furnace_actual_temp = dcs_device->furnace_actual_temp;
                actual_furnace_pressure = dcs_device->actual_furnace_pressure;
                induced_draft_fan_frequency = dcs_device->induced_draft_fan_frequency;
            }

            // 3. 更新界面显示数据
            m_furnaceActualTemp = QString::number(furnace_actual_temp, 'f', 1) + "℃";
            m_actualFurnacePressure = QString::number(actual_furnace_pressure, 'f', 2) + "Pa";
            m_inducedDraftFanFrequency = QString::number(induced_draft_fan_frequency, 'f', 1) + "Hz";

            debug_printf("数据大屏DCS数据直接获取: %s - 炉膛实际温度=%.2f, 实际炉压=%.2f, 引风机频率=%.2f\n",
                        dcsName.c_str(), furnace_actual_temp, actual_furnace_pressure, induced_draft_fan_frequency);
        } catch (...) {
            // 如果数据访问出现异常，设置为无效数据
            debug_printf("数据大屏DCS数据直接获取异常: %s\n", dcsName.c_str());
            furnace_actual_temp = actual_furnace_pressure = induced_draft_fan_frequency = 0.0f;
            m_furnaceActualTemp = "0.0℃";
            m_actualFurnacePressure = "0.00Pa";
            m_inducedDraftFanFrequency = "0.0Hz";
        }
    } else {
        // DCS未连接时显示默认值
        m_furnaceActualTemp = "0.0℃";
        m_actualFurnacePressure = "0.00Pa";
        m_inducedDraftFanFrequency = "0.0Hz";
    }

    emit dataChanged();  // 通知QML界面更新
}
```

### 数据流向总结
```
真实设备 → 通信协议 → 采集线程 → 全局映射表 → datascreen.cpp → DataScreenView.qml
   ↓           ↓          ↓         ↓            ↓              ↓
DCS系统 → OPC协议 → OPC采集线程 → dcs_opc_map → updateDcsData() → 界面显示
烟气仪 → Modbus → 烟气采集线程 → boiler_map → updateSmokeData() → 界面显示
```

### 设备连接状态处理

#### 正常连接状态
当设备正常连接时，系统会：
- 显示实时采集的数据值
- 设置连接状态为已连接
- 定期更新数据显示

#### 设备未连接状态
当设备未连接时，系统会：
- 显示默认的0值 (如 "0.00%", "0ppm", "0.0℃")
- 设置连接状态为未连接
- 继续尝试连接和数据获取

#### 典型日志输出
```
数据大屏: 发射dataChanged信号
qml: DataScreen received hardware data update
qml: Current data values - O2: 0.00% CO: 0ppm
数据大屏DCS连接状态检测: 设备名='DCS1', dcs_opc_map大小=1
数据大屏DCS OPC连接状态检测: 找到设备对象，is_initialized=true, opc_connected=false
数据大屏DCS OPC连接状态检测: ✓ OPC连接正常
调试: 请求DCS OPC设备名称: DCS1
调试: dcs_opc_map大小: 1
数据大屏DCS核心数据更新: 温度='0.0℃', 负压='0.00Pa', 引风机频率='0.0Hz'
数据大屏DCS: 发射dataChanged信号
```

### 界面布局特点
- **左侧参数区域**: 垂直排列5个核心参数，每个参数配有图标和数值显示
- **右侧图像区域**: 显示分解炉工艺流程图
- **响应式设计**: 支持不同分辨率的屏幕显示
- **实时更新**: 数据变化时界面自动刷新，无需手动操作

### 技术特点
- **高性能**: 使用Qt信号槽机制，避免轮询造成的性能损耗
- **线程安全**: 数据获取使用互斥锁保护，确保多线程环境下的数据一致性
- **容错处理**: 设备连接异常时显示默认值，不影响界面正常运行
- **模块化设计**: 烟气和DCS数据获取相互独立，便于维护和扩展

## 部署和维护

