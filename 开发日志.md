# Precalciner 项目开发日志

## 文档更新日志

### 2025-07-16
- **新增**：DCS OPC设备与CSV文件管理的关联机制详解章节
  - 详细说明了dcsopc.cpp和csvfile.cpp之间的3个核心函数调用关系
  - init_csv_manager()初始化、write_to_csv_buffer()数据写入、close_csv_manager()清理的完整流程
  - 数据统一管理机制：烟气分析仪只采集，DCS设备统一写入CSV
  - CSV文件按日期管理的详细实现机制和文件命名规则
  - 13个参数的数据来源分布和CSV格式说明
  - 自动化功能特性和设计优势分析
- **目的**：帮助开发者理解系统的数据存储架构和CSV文件管理的完整机制

### 2025-07-15
- **新增**：DCS OPC设备全局映射表 (dcs_opc_map) 详解章节
  - 详细说明了dcs_opc_map的数据结构定义和初始化流程
  - DCSOPCDevice对象数据存储机制和数据更新流程
  - 全局访问模式和线程安全机制
  - 使用该映射表的所有模块列表
- **新增**：DataScreenView.qml 数据大屏页面详解章节
  - 完整的页面架构和数据更新机制说明
  - 5个核心参数的数据来源和显示逻辑
  - 烟气分析仪和DCS数据获取的详细代码流程
  - 设备连接状态处理和容错机制
  - 典型日志输出分析和界面布局特点
- **目的**：为团队提供完整的系统核心数据管理和界面展示机制文档
- **修正**：文档内容与实际代码同步更新
  - 更新系统架构图，移除已删除的DCS Modbus模块
  - 修正数据获取方式描述，反映datascreen.cpp的直接访问优化
  - 更新DataScreenView.qml章节中的代码示例，展示最新的直接访问实现
  - 修正主要功能描述，明确DCS只使用OPC协议
  - 更新选择原则，反映性能优化后的最佳实践
  - 修正参数调整逻辑描述，反映氧气浓度调节不再使用三次风的变化
  - 更新业务逻辑流程图，展示生料量/煤量调节替代三次风调节
  - 同步更新OPC_DA_通讯说明.md中的调整流程描述

### 2025-01-14
- **新增**：数据获取方式详解章节
  - 详细说明了DCS和烟气分析仪的两种数据获取模式
  - 直接访问模式 vs 函数接口模式的使用场景
  - 各模块数据获取方式总览表
- **新增**：数据获取实际代码示例
  - parameter_adjustment.cpp 的数据获取示例
  - datascreen.cpp 的数据获取示例
  - 数据获取方式选择指南
- **目的**：帮助开发者快速理解系统中不同模块如何获取DCS和烟气数据

### 系统要求
- **操作系统**：Windows 10/11 (OPC DA需要)
- **Qt版本**：Qt 6.9.1或更高版本
- **编译器**：MinGW 64-bit
- **硬件**：支持RS485通讯的串口设备

### 配置文件说明
详细的config.ini配置说明请参考《OPC_DA_通讯说明.md》文档。

### 故障排除
1. **串口连接问题**：检查COM端口和波特率配置
2. **OPC连接问题**：确认OPC服务器运行状态和ProgID
3. **数据采集异常**：查看调试日志，检查设备地址配置
4. **参数调整失效**：确认DCS写入权限和标签配置

该系统为水泥厂分解炉提供了完整的数据采集、监控和智能调整解决方案，具有高可靠性、易维护性和良好的扩展性。

## 版本更新说明

### 参数调整设备连接检查优化 (2025-07-16)
- **需求背景**：用户要求在parameter_adjustment.cpp中先检查烟气分析仪和DCS连接状态，如果设备未连接则不执行后续调整操作
- **解决方案**：在`check_and_adjust_parameters`函数开始处添加设备连接状态检查逻辑
- **技术实现**：
  - **DCS OPC连接检查**：使用`DCSOPCDevice::is_opc_connected()`方法检查OPC连接状态
  - **烟气分析仪连接检查**：检查`Boiler::fd >= 0`状态判断串口连接是否正常
  - **提前退出机制**：任一设备未连接时直接return，不执行后续所有调整操作
- **代码修改**：
  - `parameter_adjustment.cpp`：在`check_and_adjust_parameters`函数开始处添加设备连接检查
  - 优化代码逻辑：避免重复查找boiler_map，直接使用已检查的设备对象
  - 添加详细的连接状态日志输出，便于故障诊断
- **效果**：
  - 避免设备未连接时执行无效的参数调整操作
  - 提供清晰的设备连接状态反馈
  - 提高系统稳定性和可靠性
- **日志示例**：
  ```
  参数调整: DCS OPC设备 'DCS1' 未连接，停止所有调整操作
  参数调整: 烟气分析仪 '分解炉1号' 未连接（fd=-1），停止所有调整操作
  参数调整: 设备连接检查通过 - DCS OPC: DCS1 (已连接), 烟气分析仪: 分解炉1号 (fd=3)
  ```

### 数据大屏设备连接检查性能优化 (2025-07-16)
- **问题背景**：用户发现DataScreenView.qml和datascreen.cpp也存在设备未连接时仍持续更新数据的性能问题
- **问题分析**：
  - 定时器持续运行，每隔几秒触发`updateData()`
  - 即使设备未连接，仍执行无效的设备查询和状态检查
  - 产生大量"设备未连接"的调试日志，浪费CPU和内存资源
- **解决方案**：在`updateData()`函数开始处添加设备连接状态检查，类似参数调整页面的优化
- **技术实现**：
  - **提前连接检查**：在`updateData()`开始处检查烟气分析仪和DCS设备连接状态
  - **提前退出机制**：如果所有设备均未连接，直接设置默认值并返回，避免后续无效操作
  - **简化子函数**：优化`updateSmokeData()`和`updateDcsData()`，移除重复的连接检查逻辑
  - **性能优化**：减少无效的设备映射查找和状态检查操作
- **代码修改**：
  - `datascreen.cpp`：在`updateData()`函数添加设备连接预检查逻辑
  - 简化`updateSmokeData()`和`updateDcsData()`函数，移除冗余的连接检查
  - 优化日志输出，减少设备未连接时的调试信息
- **效果**：
  - 显著减少设备未连接时的CPU和内存开销
  - 避免无效的设备查询和数据处理操作
  - 提高数据大屏页面的整体性能
  - 减少无用的调试日志输出
- **日志示例**：
  ```
  数据大屏: 所有设备均未连接，跳过数据更新以节省性能
  数据大屏: 检测到设备连接，继续执行数据更新
  数据大屏连接检查: 烟气分析仪 '分解炉1号' 已连接 (fd=3)
  数据大屏连接检查: DCS设备 'DCS1' 已连接
  ```

### Qt Quick样式警告修复 (2025-07-16)
- **问题背景**：ParameterAdjustmentView.qml页面出现Qt样式警告信息
- **警告内容**：
  ```
  QML QQuickText: The current style does not support customization of this control (property: "contentItem")
  Please customize a non-native style (such as Basic, Fusion, Material, etc).
  ```
- **问题分析**：
  - 在QML中使用了`contentItem`属性自定义按钮和控件外观
  - 系统默认使用Windows原生样式，不支持深度自定义
  - 这是警告而非错误，程序仍正常运行
- **解决方案**：在main.cpp中设置Qt Quick样式为Fusion
- **代码修改**：
  - 添加`#include <QQuickStyle>`头文件
  - 在main()函数中添加`QQuickStyle::setStyle("Fusion")`
- **效果**：
  - 消除Qt样式自定义警告信息
  - 保持原有的UI自定义效果
  - 提供更好的跨平台一致性

### OPC连接重连间隔限制优化 (2025-07-16)
- **问题背景**：客户现场程序卡死，分析发现是OPC连接失败时无限重连导致的阻塞
- **解决方案**：为DCSOPCDevice添加重连间隔和次数限制机制
- **技术实现**：
  - 添加重连控制变量：`last_connect_attempt`、`connect_retry_count`
  - 设置重连间隔：60秒内不重复尝试连接
  - 设置最大重连次数：最多尝试3次重连
  - 重连成功后自动重置计数器
- **代码修改**：
  - `dcsopc.h`：添加重连控制相关成员变量和常量
  - `dcsopc.cpp`：修改`read_data()`函数，增加重连频率和次数限制逻辑
  - 构造函数中初始化重连控制时间戳
- **效果**：
  - 避免OPC连接失败时的无限阻塞，程序不再卡死
  - 减少不必要的重连尝试，降低系统资源消耗
  - 提供详细的重连状态日志，便于故障诊断
- **注意事项**：烟气分析仪连接失败时会直接退出线程，不存在无限重连问题

### 烟气分析仪通讯模块重命名优化 (2025-07-08)
- **data.cpp/.h** → **smoke_analyzer_comm.cpp/.h**
- **优化目的**：明确模块职责，smoke_analyzer_comm专门负责烟气分析仪RS485通讯
- **影响范围**：涉及所有引用data.h的文件，包括main.cpp、monitoring_datasource.cpp等8个文件
- **技术优势**：
  - 命名更准确，避免与通用"data"概念混淆
  - 与项目命名规范一致（如dcsopc.cpp）
  - 提高代码可读性和可维护性
  - 便于新开发者理解模块功能

### 数据源重命名优化 (2025-07-08)
- **datasource.cpp/.h** → **monitoring_datasource.cpp/.h**
- **DataSource类** → **MonitoringDataSource类**
- **优化目的**：明确职责分工，MonitoringDataSource专门服务于MonitoringSystem.qml监控界面
- **影响范围**：仅影响C++后端实现，QML界面无需修改
- **技术优势**：提高代码可读性，避免与其他数据源类混淆，便于团队协作和维护

### 开关量信号采集功能新增 (2025-07-09)
- **功能概述**：为烟气分析仪监控系统新增开关量信号（数字信号）采集和显示功能
- **配置新增**：
  - `config.ini`中添加`Switch1 = 70`配置项，支持开关量信号设备地址配置
  - 详细的配置注释说明，明确各传感器的Modbus设备地址含义
- **后端数据采集层修改**：
  - `boiler.h/.cpp`：添加开关量信号设备地址配置和数据存储变量
  - `smoke_analyzer_comm.h/.cpp`：更新`get_realtime_data()`函数支持开关量信号参数
  - `monitoring_datasource.h/.cpp`：添加开关量信号的数据处理、存储和表格显示逻辑
  - `datascreen.cpp`、`main.cpp`：更新函数调用以支持新的开关量信号参数
- **前端界面层修改**：
  - `MonitoringSystem.qml`：在当前数据显示区域添加"开关信号量"状态显示
  - 数据表格中新增"开关信号量"列，显示运行/停止状态
  - 历史数据查询完整支持开关量信号的记录和显示

### QML日志输出乱码修复 (2025-07-11)
- **问题描述**：QML页面中的console.log输出中文字符时出现乱码，影响调试和日志查看
- **解决方案**：
  - **QML文件修改**：将所有QML文件中的中文console.log输出改为英文
    - `MonitoringSystem.qml`：修复5处中文console.log输出
    - `ParameterAdjustmentView.qml`：修复6处中文console.log输出
  - **C++编码修复**：
    - `csvreader.cpp`：修复QString中文字符传递时的编码问题，使用toUtf8()确保正确显示

### 生料量调节步长配置优化 (2025-07-14)
- **功能概述**：为生料量调节功能添加可配置的调节步长，替代原有的80%差值调整策略
- **配置新增**：
  - `config.ini`中添加`RawMaterialAdjustmentStep = 5.0`配置项，支持自定义生料量调节步长
  - 详细的配置注释说明，明确调节步长的作用和单位（kg）
- **后端代码修改**：
  - `parameter_adjustment.h`：添加`raw_material_adjustment_step`成员变量
  - `parameter_adjustment.cpp`：
    - 构造函数中读取`RawMaterialAdjustmentStep`配置项
    - 修改`adjust_raw_material_parameter()`函数，使用配置的步长替代80%差值策略
    - 修改`calculate_adjustment_suggestion()`函数，保持手动模式与自动模式调节逻辑一致
    - 更新调试输出信息，显示使用的调节步长
- **技术优势**：
  - 提供更精确的生料量调节控制，避免过度调整
  - 支持根据实际工艺需求配置合适的调节步长
  - 保持调节逻辑的一致性和可预测性
  - 便于运维人员根据现场情况优化调节参数

### DCS Modbus通信模块清理 (2025-07-14)
- **背景说明**：DCS系统已全面切换到OPC通信，不再使用Modbus串口通信，需要清理相关代码和配置
- **删除的文件**：
  - `dcs.cpp` - DCS Modbus通信实现文件
  - `dcs.h` - DCS Modbus通信头文件
- **配置文件清理**：
  - `config.ini`：删除所有DCS Modbus相关配置项
    - 删除`DeviceAddress`、`StartRegister`、`RegisterCount`等Modbus通信参数
    - 删除各种数据偏移量配置（`FurnaceSetTempOffset`等）
    - 删除DCS寄存器地址配置（`PlannedRawMaterialWriteAddress`等）
    - 简化通信方式配置，只保留OPC通信
- **代码文件修改**：
  - `main.cpp`：删除dcs.h引用和Modbus设备相关代码
  - `parameter_adjustment.cpp/.h`：
    - 删除所有Modbus通信分支，统一使用OPC通信
    - 删除Modbus寄存器地址相关成员变量和配置读取
    - 简化调节函数，移除通信类型判断逻辑
  - `datascreen.cpp`：删除Modbus DCS设备检查，只保留OPC设备
  - `configmanager_qml.cpp`：删除DCS Modbus设备配置更新逻辑
  - `PrecalcinerBurning.pro`：从项目文件中移除dcs.cpp和dcs.h
- **注意事项**：烟气分析仪仍使用Modbus通信，相关代码保持不变

### 氧气浓度调节逻辑更新 (2025-07-14)
- **背景说明**：根据实际工艺需求，氧气浓度调节不再使用三次风，改为通过调节生料量和煤量来控制氧气浓度
- **配置文件修改**：
  - `config.ini`：
    - 删除三次风相关OPC标签配置（`OPCTertiaryAirFrequencyTag`、`OPCTertiaryAirWriteTag`）
    - 删除三次风调节步长配置（`TertiaryAirAdjustmentStep`）
    - 新增氧气浓度调节配置：
      - `OxygenRawMaterialAdjustmentStep = 3.0` - 氧气浓度调节生料量步长（kg）
      - `OxygenCoalFeedAdjustmentStep = 5.0` - 氧气浓度调节煤量步长（kg）
    - 更新控制流程说明，明确新的氧气浓度调节逻辑
- **DCS OPC通信模块修改**：
  - `dcsopc.h/.cpp`：
    - 删除三次风频率数据采集和存储相关代码
    - 删除三次风OPC标签配置和读写功能
    - 更新`get_realtime_dcs_opc_data`函数，移除三次风频率参数
    - 简化OPC项目列表，不再包含三次风相关标签
- **参数调整逻辑重构**：
  - `parameter_adjustment.h/.cpp`：
    - 删除三次风调节相关配置参数和函数
    - 删除`adjust_tertiary_air_parameter`函数和`send_tertiary_air_adjustment_to_dcs`函数
    - 删除`get_tertiary_air_adjustment_suggestion` C接口函数
    - 重构`adjust_oxygen_concentration_parameter`函数：
      - 氧气浓度 > 设定值：增加生料量（使用`OxygenRawMaterialAdjustmentStep`）
      - 氧气浓度 < 设定值：减少煤量（使用`OxygenCoalFeedAdjustmentStep`）
    - 更新步骤2的氧气浓度调节逻辑，使用生料量和煤量调节替代三次风调节
- **界面显示更新**：
  - `ParameterAdjustmentView.qml`：删除三次风频率的显示和调节建议
  - `datascreen.h/.cpp`：删除三次风频率相关属性和数据更新逻辑
  - `main.cpp`：更新DCS数据获取函数调用，移除三次风频率参数

### 氧气浓度调节流程逻辑修复 (2025-07-14)
- **问题描述**：`adjust_oxygen_concentration_parameter`函数中存在不符合控制流程的逻辑
- **问题分析**：
  - 原代码在步骤2（氧气浓度调节）中重新判断炉膛温度，不符合config.ini中的控制流程
  - 按照设计，进入步骤2时炉膛温度已经在范围内，不应该再重新判断
- **修复内容**：
  - `parameter_adjustment.cpp`：
    - 更新`adjust_oxygen_concentration_parameter`函数注释，明确步骤2的执行条件
    - 删除重新判断炉膛温度的代码逻辑（第1115-1139行）
    - 简化函数逻辑，直接进行氧气浓度调节，不再重复温度检查

### 参数调整页面数据获取优化 (2025-07-14)
- **优化背景**：参数调整页面只显示3个参数，但getCurrentDCSValues函数返回了6个参数，存在数据冗余
- **优化内容**：
  - `parameter_adjustment_qml.cpp`：
    - 简化`getCurrentDCSValues`函数，只返回参数调整页面需要的3个参数
    - 删除不需要的参数：`plannedRawMaterial`（计划生料量）、`furnaceActualTemp`（炉膛实际温度）、`actualFurnacePressure`（实际炉膛压力）
    - 保留必要参数：`actualRawMaterial`（实际生料量）、`coalFeedRate`（给煤量）、`inducedDraftFanFrequency`（引风机频率）
  - `parameter_adjustment_qml.h`：更新函数注释，明确只返回3个参数
  - `ParameterAdjustmentView.qml`：更新辅助函数注释
- **优化效果**：
  - 减少50%的数据传输量（从6个参数减少到3个）
  - 提升函数性能和代码可维护性
  - 明确函数用途，避免数据冗余

### 数据大屏页面数据获取优化 (2025-07-15)
- **优化背景**：datascreen.cpp存在重复的数据获取调用，先通过dcs_opc_map检查状态，再调用get_realtime_dcs_opc_data()函数（内部又查找dcs_opc_map）
- **问题分析**：
  - DCS数据获取：先访问dcs_opc_map检查状态，再调用get_realtime_dcs_opc_data()（内部重复查找dcs_opc_map）
  - 烟气数据获取：先访问boiler_map检查状态，再调用get_realtime_data()（内部重复查找boiler_map）
  - 造成不必要的性能开销和代码重复
- **优化内容**：
  - `datascreen.cpp`：
    - **DCS数据获取**：改为直接访问模式，消除get_realtime_dcs_opc_data()调用
    - **烟气数据获取**：改为直接访问模式，消除get_realtime_data()调用
    - 保持原有的设备状态检查逻辑
    - 使用互斥锁保护数据读取，确保线程安全
    - 只获取数据大屏实际需要的核心参数（DCS 3个，烟气 2个）
- **优化效果**：
  - 消除重复的映射表查找操作，提升性能
  - 减少函数调用开销
  - 代码逻辑更清晰，与parameter_adjustment_qml.cpp保持一致
  - 保持线程安全和异常处理机制

### 反吹反馈延迟时间配置优化 (2025-07-18)
- **需求背景**：用户要求在config.ini中添加可配置的延迟时间参数，用于控制反吹反馈开关量信号停止后的数据恢复延迟
- **功能描述**：
  - 当系统检测到反吹反馈开关量信号运行时（Switch1=0），MonitoringSystem.qml和DataScreenView.qml页面上氧气、一氧化碳数值停止更新
  - 当系统检测到反馈开关量信号停止时（Switch1=1），延迟指定时间后，恢复氧气、一氧化碳数值的实时更新
  - 延迟时间现在可以通过配置文件进行自定义设置
- **配置新增**：
  - `config.ini`中添加`BackflowDelayTime = 60`配置项，支持自定义反吹反馈停止后的延迟恢复时间
  - 详细的配置注释说明，明确反吹反馈控制逻辑和延迟时间的作用
  - 单位：秒，默认值：60秒
- **技术优势**：
  - 提供灵活的延迟时间配置，适应不同工艺需求
  - 避免硬编码延迟时间，提高系统可配置性
  - 便于运维人员根据现场情况调整延迟参数
  - 保持配置文件的结构化和可读性

### 反吹反馈延迟控制功能实现 (2025-07-18)
- **需求背景**：用户要求实现反吹反馈控制功能，当系统检测到反吹反馈开关量信号运行时（Switch1=0），MonitoringSystem.qml和DataScreenView.qml页面上氧气、一氧化碳数值停止更新，当系统检测到反馈开关量信号停止时（Switch1=1），延迟可配置的时间后，恢复氧气、一氧化碳数值的实时更新
- **功能实现**：
  - `config.ini`：新增`BackflowDelayTime = 60`配置项，支持自定义反吹反馈停止后的延迟恢复时间，单位：秒，默认60秒
  - `monitoring_datasource.h/.cpp`：
    - 新增反吹反馈控制相关成员变量：`m_isBackflowActive`、`m_isDataUpdateSuspended`、`m_backflowDelayTimer`等
    - 新增核心方法：`checkBackflowStatus()`、`suspendO2COUpdates()`、`resumeO2COUpdates()`、`getBackflowDelayTime()`
    - 修改数据更新逻辑：只在`!m_isDataUpdateSuspended`时更新O2/CO的图表数据和表格数据
  - `datascreen.h/.cpp`：
    - 新增相同的反吹反馈控制变量和方法
    - 修改`updateSmokeData()`：只在`!m_isDataUpdateSuspended`时更新`m_oxygenContent`和`m_coContent`
- **技术特点**：
  - **选择性数据更新**：仅暂停氧气(O2)和一氧化碳(CO)数据更新，其他参数（NOx、SO2、温度、压力、电流）继续正常更新
  - **状态管理机制**：通过开关量信号状态变化检测反吹反馈的开始和停止，使用QTimer实现延迟恢复
  - **配置驱动设计**：延迟时间通过配置文件设置，支持运行时调整，配置读取失败时使用60秒默认值
- **实现效果**：
  - 反吹期间O2/CO数值保持显示上一次的值，图表线条停止增长，表格不添加新行
  - 反吹停止后延迟指定时间恢复数据更新，避免显示不准确的数据
  - 其他监控参数不受影响，保持系统完整监控能力
  - 提供灵活的延迟时间配置，适应不同工艺需求

### 反吹反馈控制系统完整状态转换流程

#### 数据源统一
所有模块都通过 `boiler_map` 获取开关量信号状态：
```cpp
extern std::unordered_map<std::string, Boiler*> boiler_map;
int switch1 = boiler_device->switch1;  // 获取开关量信号
```

#### 完整状态转换流程图（更新后）
```
正常状态 (Switch1=0)
├── UI层: 氧气、一氧化碳正常更新显示
├── 参数调整层: 正常执行调节逻辑
└── 图表/表格: 正常添加数据点和表格行
    ↓ Switch1: 0→1 (反吹开始)

反吹激活状态 (Switch1=1)
├── UI层: 氧气、一氧化碳停止更新，保持上一次数值
├── 参数调整层: 继续正常执行调节逻辑 (不受影响)
├── 图表: O2/CO线条停止增长，其他参数继续
└── 表格: 停止添加新行
    ↓ Switch1: 1→0 (反吹停止)

反吹停止，延迟期间 (Switch1=1, 延迟计时中)
├── UI层: 氧气、一氧化碳仍然停止更新
├── 参数调整层: 继续正常执行调节逻辑 (不受影响)
├── 图表: O2/CO线条仍然停止增长
├── 表格: 仍然停止添加新行
└── 延迟定时器: 倒计时 BackflowDelayTime 秒
    ↓ 延迟时间到达

延迟结束，完全恢复 (Switch1=1, 延迟结束)
├── UI层: 氧气、一氧化碳恢复实时更新
├── 参数调整层: 继续正常执行调节逻辑 (一直正常)
├── 图表: O2/CO线条恢复增长，显示最新数据
└── 表格: 恢复添加新行
```

#### 各层控制机制（更新后）
- **UI层控制** (monitoring_datasource.cpp, datascreen.cpp)
  - 通过 `m_isDataUpdateSuspended` 标志控制O2/CO数据更新
  - 使用 `QTimer` 实现延迟恢复机制
  - 只影响氧气和一氧化碳，其他参数正常显示

- **参数调整层** (parameter_adjustment.cpp)
  - 不受反吹反馈状态影响，继续正常执行所有调节逻辑
  - 基于DCS数据进行参数调整，DCS数据不受烟气分析仪反吹影响
  - 保持原有的调整流程：生料量、炉温、氧气浓度等调整正常进行

- **配置管理**
  - 使用 `BackflowDelayTime` 配置项控制UI层延迟时间
  - 默认60秒，支持运行时配置调整

### 参数调整逻辑需求变更 (2025-07-18)
- **需求变更**：经过重新评估，决定参数调整逻辑不受反吹反馈和延迟期间的影响，只有UI层的数据显示需要控制
- **变更原因**：参数调整逻辑基于DCS数据进行，而DCS数据不受烟气分析仪反吹反馈的影响，因此可以继续正常执行调整
- **代码回滚**：
  - `parameter_adjustment.h`：移除反吹反馈控制相关方法声明：`isBackflowActive()`、`isInBackflowDelayPeriod()`、`getBackflowDelayTime()`
  - `parameter_adjustment.cpp`：移除反吹反馈状态检查逻辑，恢复原有的参数调整流程
- **最终实现**：
  - **UI层控制**：MonitoringSystem.qml和DataScreenView.qml在反吹和延迟期间停止显示氧气、一氧化碳数值更新
  - **参数调整层**：继续正常执行所有参数调整逻辑，不受反吹反馈状态影响
  - **数据分离**：UI显示控制和参数调整逻辑完全独立，各自基于不同的数据源和业务需求

### 烟气分析仪设备地址配置界面扩展 (2025-07-18)
- **需求背景**：用户需要在CollectionConfigView.qml配置页面中方便地配置烟气分析仪的各种设备地址参数，包括传感器地址、采集间隔和反吹延迟时间等
- **功能实现**：
  - `CollectionConfigView.qml`：在RS485烟气分析仪配置区域右侧新增"烟气分析仪设备地址配置"面板
  - 新增配置项包括：
    - **数据采集间隔**：CollectionInterval，默认15秒，范围1-300秒
    - **传感器设备地址**：NOx(30)、电流传感器(60)、SO2(30)、压力表传感器(50)、O2(4)、CO(1)、温度传感器(40)、开关量信号1(80)
    - **反吹延迟时间**：BackflowDelayTime，默认60秒，范围1-600秒
  - 所有配置项都支持输入验证，设备地址范围1-255，时间参数有合理的上下限
  - 配置项变更时自动标记为未保存状态，与现有的保存机制集成
- **界面设计**：
  - 使用ScrollView支持配置项较多时的滚动显示
  - 采用GridLayout布局，标签右对齐，输入框统一宽度120px
  - 保持与现有配置界面一致的视觉风格和交互体验
- **技术特点**：
  - **配置完整性**：涵盖config.ini中烟气分析仪相关的所有Modbus设备地址配置
  - **数据验证**：使用IntValidator确保输入的设备地址和时间参数在有效范围内
  - **状态管理**：与现有的hasUnsavedChanges机制集成，支持配置变更检测和保存提醒
- **实现效果**：
  - 用户可以在图形界面中直接配置所有烟气分析仪相关参数，无需手动编辑config.ini文件
  - 提供直观的配置界面，降低配置错误的可能性
  - 支持实时配置验证和保存状态提示，提升用户体验

### 配置界面布局样式优化 (2025-07-18)
- **问题描述**：CollectionConfigView.qml页面中RS485烟气分析仪配置和设备地址配置挤在一起，布局混乱，影响用户体验
- **问题分析**：
  - 原有布局使用ColumnLayout垂直排列，导致两个配置区域上下堆叠
  - RS485配置项缺少ScrollView，配置项过多时显示不完整
  - 左右两个配置面板样式不一致，视觉效果不协调
- **优化方案**：
  - `CollectionConfigView.qml`：将RS485配置区域改为RowLayout左右并排布局
  - **左侧面板**：RS485串口配置，使用ScrollView + GridLayout(2列)，标签右对齐
  - **右侧面板**：烟气分析仪设备地址配置，保持原有ScrollView + GridLayout(2列)布局
  - 统一两个面板的样式：白色背景、圆角12px、边框颜色#e0e0e0
- **布局改进**：
  - 使用RowLayout实现左右并排显示，充分利用屏幕宽度
  - 两个面板都添加ScrollView，支持配置项较多时的滚动显示
  - 统一GridLayout为2列布局，标签右对齐，输入框宽度120px
  - 保持一致的间距和颜色主题
- **实现效果**：
  - 配置界面布局清晰，左右两个面板并排显示，不再挤在一起
  - 视觉效果协调统一，提升用户体验
  - 支持滚动显示，适应不同屏幕尺寸和配置项数量

### 系统托盘功能实现 (2025-07-18)
- **需求背景**：用户要求点击右上角关闭按钮（X）时不直接退出程序，而是弹出提示框让用户选择是否退出，或者将程序最小化到系统托盘（任务栏通知区域）
- **功能实现**：
  - **新增文件**：
    - `systemtray.h/.cpp`：系统托盘管理器类，负责托盘图标显示、菜单管理和窗口控制
    - 提供QML接口方法：`showMainWindow()`、`hideToTray()`、`quitApplication()`、`showTrayMessage()`
  - **主程序集成**：
    - `main.cpp`：创建SystemTray实例，注册到QML上下文，设置应用程序退出策略为`setQuitOnLastWindowClosed(false)`
    - 修复QStyle头文件包含问题，确保托盘图标正常显示
  - **界面优化**：
    - `main.qml`：添加`onClosing`事件处理，阻止默认关闭行为，显示美观的退出确认对话框
    - 重新设计退出确认对话框：使用渐变背景、圆角设计、图标提示、阴影效果
    - 提供两个选项：**最小化到托盘**（蓝色渐变按钮）和**退出程序**（红色渐变按钮）
  - **资源文件更新**：
    - `qml.qrc`：添加app-icon.ico资源，确保托盘图标正常显示
    - `PrecalcinerBurning.pro`：添加systemtray.cpp/.h到项目文件
- **技术特点**：
  - **系统托盘集成**：使用QSystemTrayIcon实现托盘图标显示，支持双击显示窗口、右键菜单操作
  - **优雅的退出流程**：用户可选择最小化到托盘继续后台运行，或完全退出程序
  - **美观的UI设计**：退出确认对话框采用现代化设计，包含图标、渐变按钮、阴影效果
  - **跨平台兼容**：自动检测系统托盘可用性，不可用时降级为隐藏窗口
- **用户体验提升**：
  - **防误操作**：避免用户意外关闭程序导致数据采集中断
  - **后台运行**：支持最小化到托盘，程序在后台继续运行数据采集和监控
  - **快速恢复**：双击托盘图标或右键菜单可快速恢复窗口显示
  - **托盘提示**：最小化时显示友好的提示消息，告知用户如何恢复窗口
- **实现效果**：
  - 点击窗口关闭按钮弹出精美的确认对话框，提供明确的操作选择
  - 选择"最小化到托盘"后程序隐藏到系统托盘，继续后台运行
  - 选择"退出程序"后完全关闭应用程序和所有后台线程
  - 托盘图标支持双击恢复窗口、右键菜单操作，用户体验友好