import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12

Page {
    id: parameterPage

    // 当前调整模式状态（从配置文件读取）
    property bool isAutoMode: false

    // 生料量设定目标选择状态（1=生料量1设定，2=生料量2设定）
    property int rawMaterialSetpointTarget: 2

    // 生料量设定调整建议（同时存储两个生料量设定的建议）
    property var rawMaterialSetSuggestion: ({
        hasSuggestion: false,
        rawMaterial1SetSuggested: 0.0,
        rawMaterial1SetCurrent: 0.0,
        rawMaterial2SetSuggested: 0.0,
        rawMaterial2SetCurrent: 0.0,
        unit: "kg",
        reason: ""
    })

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                onClicked: stackView.pop()
            }
            Label {
                text: "分解炉1号燃烧参数调整"
                font.pixelSize: 20
                font.bold: true
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }

            // 右上角按钮
            Button {
                text: "手动调整"
                Layout.preferredWidth: 120
                Layout.preferredHeight: 35
                enabled: isAutoMode  // 只有在自动模式时才能切换到手动模式
                background: Rectangle {
                    color: {
                        if (!parent.enabled) return "#666666"  // 禁用状态
                        if (!isAutoMode) return "#1976d2"      // 当前模式（激活状态）
                        return parent.pressed ? "#1976d2" : "#2196f3"  // 可切换状态
                    }
                    radius: 6
                    border.width: isAutoMode ? 0 : 2
                    border.color: "#ffffff"
                }
                contentItem: Text {
                    text: parent.text + (isAutoMode ? "" : " (当前)")
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: {
                    // 显示手动调整确认对话框
                    manualAdjustmentDialog.open()
                }
            }

            Button {
                text: "自动调整"
                Layout.preferredWidth: 120
                Layout.preferredHeight: 35
                enabled: !isAutoMode  // 只有在手动模式时才能切换到自动模式
                background: Rectangle {
                    color: {
                        if (!parent.enabled) return "#666666"  // 禁用状态
                        if (isAutoMode) return "#388e3c"       // 当前模式（激活状态）
                        return parent.pressed ? "#388e3c" : "#4caf50"  // 可切换状态
                    }
                    radius: 6
                    border.width: isAutoMode ? 2 : 0
                    border.color: "#ffffff"
                }
                contentItem: Text {
                    text: parent.text + (isAutoMode ? " (当前)" : "")
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: {
                    // 显示自动调整确认对话框
                    autoAdjustmentDialog.open()
                }
            }
        }
    }

    // 主要内容区域
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 40
        spacing: 30

        // 页面标题
        Label {
            text: "分解炉1号燃烧参数调整"
            font.pixelSize: 28
            font.bold: true
            color: "#ffffff"
            Layout.alignment: Qt.AlignHCenter
        }

        // 垂直居中的容器
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true

            // 居中的表格
            Rectangle {
                anchors.centerIn: parent
                width: Math.min(parent.width * 0.85, 1100)
                height: Math.min(parent.height * 0.95, 900)
                color: "#ffffff"
                radius: 15
                border.color: "#e0e0e0"
                border.width: 2

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15

                    // 用户设置区域
                    Rectangle {
                        Layout.fillWidth: true
                        height: 120  // 增加高度以容纳换行内容
                        color: "#f0f8ff"
                        radius: 8
                        border.color: "#2196f3"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15

                            // 第一行：给定炉压、设定氧气浓度、临时炉膛设定温度、临时计划生料量
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 30

                                // 给定炉压设置
                                RowLayout {
                                    spacing: 10

                                    Label {
                                        text: "给定炉压:"
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#333333"
                                    }

                                    TextField {
                                        id: furnacePressureInput
                                        width: 80
                                        height: 35
                                        text: "-75.0"
                                        font.pixelSize: 12
                                        horizontalAlignment: TextInput.AlignHCenter
                                        validator: DoubleValidator {
                                            bottom: -100.0
                                            top: -50.0
                                            decimals: 1
                                        }
                                        background: Rectangle {
                                            color: "#ffffff"
                                            border.color: parent.activeFocus ? "#2196f3" : "#cccccc"
                                            border.width: 1
                                            radius: 4
                                        }
                                        // 移除自动保存，改为手动确认
                                    }

                                    Label {
                                        text: "Pa"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    Label {
                                        text: "(-100~-50)"
                                        font.pixelSize: 10
                                        color: "#999999"
                                    }
                                }

                                // 设定氧气浓度设置
                                RowLayout {
                                    spacing: 10

                                    Label {
                                        text: "设定氧气浓度:"
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#333333"
                                    }

                                    TextField {
                                        id: oxygenConcentrationInput
                                        width: 80
                                        height: 35
                                        text: "3.5"
                                        font.pixelSize: 12
                                        horizontalAlignment: TextInput.AlignHCenter
                                        validator: DoubleValidator {
                                            bottom: 0.0
                                            top: 21.0
                                            decimals: 2
                                        }
                                        background: Rectangle {
                                            color: "#ffffff"
                                            border.color: parent.activeFocus ? "#2196f3" : "#cccccc"
                                            border.width: 1
                                            radius: 4
                                        }
                                        // 移除自动保存，改为手动确认
                                    }

                                    Label {
                                        text: "%"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    Label {
                                        text: "(0~21)"
                                        font.pixelSize: 10
                                        color: "#999999"
                                    }
                                }

                                // 临时炉膛设定温度设置
                                RowLayout {
                                    spacing: 10

                                    Label {
                                        text: "临时炉膛设定温度:"
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#333333"
                                    }

                                    TextField {
                                        id: tempFurnaceSetTempInput
                                        width: 80
                                        height: 35
                                        text: "1000.0"
                                        font.pixelSize: 12
                                        horizontalAlignment: TextInput.AlignHCenter
                                        validator: DoubleValidator {
                                            bottom: 800.0
                                            top: 1200.0
                                            decimals: 1
                                        }
                                        background: Rectangle {
                                            color: "#ffffff"
                                            border.color: parent.activeFocus ? "#2196f3" : "#cccccc"
                                            border.width: 1
                                            radius: 4
                                        }
                                    }

                                    Label {
                                        text: "℃"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    Label {
                                        text: "(800~1200)"
                                        font.pixelSize: 10
                                        color: "#999999"
                                    }
                                }

                                // 临时计划生料量设置
                                RowLayout {
                                    spacing: 10

                                    Label {
                                        text: "临时计划生料量:"
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#333333"
                                    }

                                    TextField {
                                        id: tempPlannedRawMaterialInput
                                        width: 80
                                        height: 35
                                        text: "5.0"
                                        font.pixelSize: 12
                                        horizontalAlignment: TextInput.AlignHCenter
                                        validator: DoubleValidator {
                                            bottom: 0.0
                                            top: 999.0
                                            decimals: 1
                                        }
                                        background: Rectangle {
                                            color: "#ffffff"
                                            border.color: parent.activeFocus ? "#2196f3" : "#cccccc"
                                            border.width: 1
                                            radius: 4
                                        }
                                    }

                                    Label {
                                        text: "t/h"
                                        font.pixelSize: 12
                                        color: "#666666"
                                    }

                                    Label {
                                        text: "(≥0)"
                                        font.pixelSize: 10
                                        color: "#999999"
                                    }
                                }
                            }

                            // 第二行：生料量设定目标选择和保存按钮
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 30

                                // 生料量设定目标选择
                                RowLayout {
                                    spacing: 10

                                    Label {
                                        text: "生料量设定调整目标:"
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#333333"
                                    }

                                    Button {
                                        id: rawMaterial1Button
                                        text: "生料量1设定"
                                        width: 100
                                        height: 35
                                        font.pixelSize: 12
                                        font.bold: true

                                        background: Rectangle {
                                            color: rawMaterialSetpointTarget === 1 ? "#4caf50" : "#e0e0e0"
                                            radius: 6
                                            border.color: rawMaterialSetpointTarget === 1 ? "#388e3c" : "#cccccc"
                                            border.width: 1
                                        }

                                        contentItem: Text {
                                            text: rawMaterial1Button.text
                                            font: rawMaterial1Button.font
                                            color: rawMaterialSetpointTarget === 1 ? "white" : "#333333"
                                            horizontalAlignment: Text.AlignHCenter
                                            verticalAlignment: Text.AlignVCenter
                                        }

                                        onClicked: {
                                            rawMaterialSetpointTarget = 1
                                            // 立即更新参数显示，无需等待下一个调整周期
                                            updateParameterData()
                                        }
                                    }

                                    Button {
                                        id: rawMaterial2Button
                                        text: "生料量2设定"
                                        width: 100
                                        height: 35
                                        font.pixelSize: 12
                                        font.bold: true

                                        background: Rectangle {
                                            color: rawMaterialSetpointTarget === 2 ? "#4caf50" : "#e0e0e0"
                                            radius: 6
                                            border.color: rawMaterialSetpointTarget === 2 ? "#388e3c" : "#cccccc"
                                            border.width: 1
                                        }

                                        contentItem: Text {
                                            text: rawMaterial2Button.text
                                            font: rawMaterial2Button.font
                                            color: rawMaterialSetpointTarget === 2 ? "white" : "#333333"
                                            horizontalAlignment: Text.AlignHCenter
                                            verticalAlignment: Text.AlignVCenter
                                        }

                                        onClicked: {
                                            rawMaterialSetpointTarget = 2
                                            // 立即更新参数显示，无需等待下一个调整周期
                                            updateParameterData()
                                        }
                                    }

                                    // 显示当前选择的标签
                                    Label {
                                        text: "当前选择: " + (rawMaterialSetpointTarget === 1 ? "生料量1设定" : "生料量2设定")
                                        font.pixelSize: 12
                                        font.bold: true
                                        color: "#2196f3"
                                    }
                                }

                                // 保存按钮
                                Button {
                                    id: saveSettingsButton
                                    text: "保存设置"
                                    width: 80
                                    height: 35
                                    font.pixelSize: 12
                                    font.bold: true

                                    background: Rectangle {
                                        color: saveSettingsButton.pressed ? "#1976d2" : (saveSettingsButton.hovered ? "#42a5f5" : "#2196f3")
                                        radius: 6
                                        border.color: "#1976d2"
                                        border.width: 1
                                    }

                                    contentItem: Text {
                                        text: saveSettingsButton.text
                                        font: saveSettingsButton.font
                                        color: "white"
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }

                                    onClicked: {
                                        saveUserSettings()
                                    }
                                }
                            }
                        }
                    }

                    // 表头
                    Rectangle {
                        Layout.fillWidth: true
                        height: 50
                        color: "#f5f5f5"
                        radius: 8
                        border.color: "#333333"
                        border.width: 2

                        Row {
                            anchors.fill: parent

                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                border.color: "#333333"
                                border.width: 1
                                color: "transparent"

                                Label {
                                    anchors.centerIn: parent
                                    text: "因素"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#333333"
                                }
                            }

                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                border.color: "#333333"
                                border.width: 1
                                color: "transparent"

                                Label {
                                    anchors.centerIn: parent
                                    text: "实际运行参数"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#333333"
                                }
                            }

                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                border.color: "#333333"
                                border.width: 1
                                color: "transparent"

                                Label {
                                    anchors.centerIn: parent
                                    text: "设定值调整建议"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#333333"
                                }
                            }
                        }
                    }

                    // 参数数据列表
                    ListView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        model: parameterModel
                        clip: true

                        delegate: Rectangle {
                            width: parent ? parent.width : 0
                            height: 45
                            color: index % 2 === 0 ? "#ffffff" : "#f9f9f9"
                            border.color: "#333333"
                            border.width: 1

                            Row {
                                anchors.fill: parent

                                Rectangle {
                                    width: parent ? parent.width / 3 : 0
                                    height: parent ? parent.height : 0
                                    border.color: "#333333"
                                    border.width: 1
                                    color: "transparent"

                                    Label {
                                        anchors.centerIn: parent
                                        text: model.factor
                                        font.pixelSize: 14
                                        color: "#333333"
                                        font.bold: true
                                    }
                                }

                                Rectangle {
                                    width: parent ? parent.width / 3 : 0
                                    height: parent ? parent.height : 0
                                    border.color: "#333333"
                                    border.width: 1
                                    color: "transparent"

                                    Label {
                                        anchors.centerIn: parent
                                        text: model.currentValue
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#1976d2"
                                    }
                                }

                                Rectangle {
                                    width: parent ? parent.width / 3 : 0
                                    height: parent ? parent.height : 0
                                    border.color: "#333333"
                                    border.width: 1
                                    color: "transparent"

                                    Label {
                                        anchors.centerIn: parent
                                        text: model.suggestedValue
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#d32f2f"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 动态数据更新定时器 - 与DCS采集频率匹配
    Timer {
        id: dataUpdateTimer
        interval: 20000 // 默认20秒，将根据DCS实际采集间隔动态调整
        running: true
        repeat: true
        onTriggered: updateParameterData()
    }

    // 监听参数调整数据变化
    Connections {
        target: parameterAdjustmentQML
        function onAdjustmentSuggestionUpdated() {
            // 当调整建议更新时，立即更新参数显示
            updateParameterData()
        }
    }

    // 监听自动调整状态变化，可能需要重新同步定时器间隔
    Connections {
        target: parameterAdjustmentQML
        function onAutoAdjustmentStatusChanged(enabled) {
            updateTimerInterval()
        }
    }

    // 参数数据模型
    ListModel {
        id: parameterModel
    }

    // 当前调整建议属性 - 每次只有一个参数有建议值
    property string currentAdjustmentParameter: ""  // 当前需要调整的参数名称
    property string currentAdjustmentSuggestion: "--"  // 当前调整建议值
    property string currentAdjustmentReason: ""  // 当前调整原因

    // 获取当前DCS参数值的辅助函数 - 只获取参数调整页面需要的3个参数
    function getCurrentDCSValues() {
        var dcsName = "DCS1"  // TODO: 从配置或界面获取实际的DCS名称
        return parameterAdjustmentQML.getCurrentDCSValues(dcsName)
    }

    // 获取当前DCS设定值的辅助函数
    function getCurrentDCSSetValues() {
        var dcsName = "DCS1"  // TODO: 从配置或界面获取实际的DCS名称
        return parameterAdjustmentQML.getCurrentDCSSetValues(dcsName)
    }

    // 获取生料量设定调整建议的辅助函数
    function getRawMaterialSetSuggestion() {
        return parameterAdjustmentQML.getRawMaterialSetSuggestion()
    }

    // 获取当前调整建议 - 每次只有一个参数有建议值
    function getCurrentAdjustmentSuggestion() {
        // 先清空之前的建议值
        currentAdjustmentParameter = ""
        currentAdjustmentSuggestion = "--"
        currentAdjustmentReason = ""

        try {
            // 调用C++接口获取当前调整建议（不需要传递dcsName参数）
            var suggestionInfo = parameterAdjustmentQML.getCurrentAdjustmentSuggestion()

            if (suggestionInfo && suggestionInfo.hasSuggestion) {
                currentAdjustmentParameter = suggestionInfo.parameterName
                currentAdjustmentSuggestion = suggestionInfo.suggestedValue.toFixed(3) + suggestionInfo.unit
                currentAdjustmentReason = suggestionInfo.reason
            }
        } catch (error) {
            // 静默处理错误
        }
    }

    // 更新定时器间隔以匹配DCS采集频率
    function updateTimerInterval() {
        var dcsName = "DCS1"  // TODO: 从配置或界面获取实际的DCS名称

        try {
            // 获取DCS设备的采集间隔
            var collectionInterval = parameterAdjustmentQML.getDCSCollectionInterval(dcsName)
            if (collectionInterval > 0) {
                var newInterval = collectionInterval * 1000  // 转换为毫秒
                var oldInterval = dataUpdateTimer.interval
                dataUpdateTimer.interval = newInterval

            } else {

                dataUpdateTimer.interval = 20000
            }
        } catch (error) {

            dataUpdateTimer.interval = 20000
        }
    }

    // 数据更新函数 - 显示实际值和设定值调整建议
    function updateParameterData() {
        parameterModel.clear()

        // 获取当前DCS参数值
        var currentValues = getCurrentDCSValues()

        // 获取当前DCS设定值
        var currentSetValues = getCurrentDCSSetValues()

        // 获取当前调整建议（每次只有一个参数有建议值）
        getCurrentAdjustmentSuggestion()

        // 获取生料量设定调整建议（同时包含两个生料量设定的建议）
        rawMaterialSetSuggestion = getRawMaterialSetSuggestion()

        // 1. 实际总生料量（显示实际值，调整生料量1设定或生料量2设定）
        var rawMaterialSetSuggestionText = "--"
        var targetName = rawMaterialSetpointTarget === 1 ? "生料量1设定" : "生料量2设定"

        // 如果有生料量设定建议，根据用户选择显示相应的建议
        if (rawMaterialSetSuggestion.hasSuggestion) {
            if (rawMaterialSetpointTarget === 1) {
                // 将kg转换为t/h显示（除以1000）
                var suggested1 = rawMaterialSetSuggestion.rawMaterial1SetSuggested / 1000.0
                rawMaterialSetSuggestionText = "生料量1设定: " + suggested1.toFixed(3) + "t/h"
            } else {
                // 将kg转换为t/h显示（除以1000）
                var suggested2 = rawMaterialSetSuggestion.rawMaterial2SetSuggested / 1000.0
                rawMaterialSetSuggestionText = "生料量2设定: " + suggested2.toFixed(3) + "t/h"
            }
        }
        parameterModel.append({
            factor: "实际总生料量",
            currentValue: (currentValues.actualRawMaterial || 0.0).toFixed(3) + "t/h",
            suggestedValue: "不参与调整"
        })

        // 2. 实际生料量1（仅显示实际值）
        parameterModel.append({
            factor: "实际生料量1",
            currentValue: (currentValues.actualRawMaterial1 || 0.0).toFixed(3) + "t/h",
            suggestedValue: "不参与调整"
        })

        // 3. 实际生料量2（仅显示实际值）
        parameterModel.append({
            factor: "实际生料量2",
            currentValue: (currentValues.actualRawMaterial2 || 0.0).toFixed(3) + "t/h",
            suggestedValue: "不参与调整"
        })

        // 4. 实际给煤量（仅显示实际值）
        parameterModel.append({
            factor: "实际给煤量",
            currentValue: (currentValues.coalFeedRate || 0.0).toFixed(3) + "t/h",
            suggestedValue: "不参与调整"
        })

        // 5. 实际引风机转速（仅显示实际值）
        parameterModel.append({
            factor: "实际引风机转速",
            currentValue: (currentValues.inducedDraftFanSpeed || 0.0).toFixed(1) + "rpm",
            suggestedValue: "不参与调整"
        })

        // 6. 给煤量设定（显示设定值，调整给煤量设定）
        var coalFeedSetSuggestion2 = "--"
        if (currentAdjustmentParameter === "给煤量设定") {
            coalFeedSetSuggestion2 = currentAdjustmentSuggestion
        }
        parameterModel.append({
            factor: "给煤量设定",
            currentValue: (currentSetValues.coalFeedSet || 0.0).toFixed(3) + "t/h",
            suggestedValue: coalFeedSetSuggestion2
        })

        // 7. 生料量1设定（显示设定值，可能有调整建议）
        var rawMaterial1SetSuggestion = "--"
        if (rawMaterialSetSuggestion.hasSuggestion && rawMaterialSetpointTarget === 1) {
            var suggested1Set = rawMaterialSetSuggestion.rawMaterial1SetSuggested
            rawMaterial1SetSuggestion = "生料量1设定: " + suggested1Set.toFixed(3) + "t/h"
        }
        parameterModel.append({
            factor: "生料量1设定",
            currentValue: (currentSetValues.rawMaterial1Set || 0.0).toFixed(3) + "t/h",
            suggestedValue: rawMaterial1SetSuggestion
        })

        // 8. 生料量2设定（显示设定值，可能有调整建议）
        var rawMaterial2SetSuggestion = "--"
        if (rawMaterialSetSuggestion.hasSuggestion && rawMaterialSetpointTarget === 2) {
            var suggested2Set = rawMaterialSetSuggestion.rawMaterial2SetSuggested
            rawMaterial2SetSuggestion = "生料量2设定: " + suggested2Set.toFixed(3) + "t/h"
        }
        parameterModel.append({
            factor: "生料量2设定",
            currentValue: (currentSetValues.rawMaterial2Set || 0.0).toFixed(3) + "t/h",
            suggestedValue: rawMaterial2SetSuggestion
        })

        // 9. 引风机转速设定（显示设定值，调整引风机转速设定）
        var inducedDraftFanSetSuggestion = "--"
        if (currentAdjustmentParameter === "引风机转速设定") {
            inducedDraftFanSetSuggestion = currentAdjustmentSuggestion
        }
        parameterModel.append({
            factor: "引风机转速设定",
            currentValue: (currentSetValues.inducedDraftFanSet || 0.0).toFixed(1) + "rpm",
            suggestedValue: inducedDraftFanSetSuggestion
        })

        // 静默处理调整建议
    }



    // 页面加载完成后初始化数据
    Component.onCompleted: {
        // 从后端获取当前调整模式状态
        loadCurrentAdjustmentMode()
        // 更新定时器间隔以匹配DCS采集频率
        updateTimerInterval()
        // 初始化参数数据
        updateParameterData()
    }

    // 从配置文件加载当前调整模式
    function loadCurrentAdjustmentMode() {
        // 通过QML包装类获取当前自动调整状态
        var adjustmentInfo = parameterAdjustmentQML.getCurrentAdjustmentInfo("DCS1")
        if (adjustmentInfo && adjustmentInfo.hasOwnProperty('isAutoMode')) {
            isAutoMode = adjustmentInfo.isAutoMode
        } else {
            // 如果无法获取，默认为手动模式
            isAutoMode = false
        }

        // 加载用户设置的参数值
        loadUserSettings()
    }

    // 从配置文件加载用户设置的参数
    function loadUserSettings() {
        try {
            // 加载给定炉压
            var pressure = parameterAdjustmentQML.getFurnacePressureSetpoint()
            furnacePressureInput.text = pressure.toFixed(1)

            // 加载设定氧气浓度
            var concentration = parameterAdjustmentQML.getOxygenConcentrationSetpoint()
            oxygenConcentrationInput.text = concentration.toFixed(2)

            // 加载临时炉膛设定温度
            var tempFurnaceTemp = parameterAdjustmentQML.getTempFurnaceSetTemp()
            tempFurnaceSetTempInput.text = tempFurnaceTemp.toFixed(1)

            // 加载临时计划生料量
            var tempPlannedMaterial = parameterAdjustmentQML.getTempPlannedRawMaterial()
            tempPlannedRawMaterialInput.text = tempPlannedMaterial.toFixed(1)

            // 加载生料量设定目标选择
            var target = parameterAdjustmentQML.getRawMaterialSetpointTarget()
            rawMaterialSetpointTarget = target
        } catch (error) {
            // 使用默认值
            furnacePressureInput.text = "-75.0"
            oxygenConcentrationInput.text = "3.5"
            tempFurnaceSetTempInput.text = "1000.0"
            tempPlannedRawMaterialInput.text = "5.0"
        }
    }

    // 保存用户设置的参数
    function saveUserSettings() {
        try {
            // 验证给定炉压输入
            var pressure = parseFloat(furnacePressureInput.text)
            if (isNaN(pressure) || pressure < -100.0 || pressure > -50.0) {
                settingsErrorDialog.errorMessage = "给定炉压超出范围！\n有效范围：-100 ~ -50 Pa\n当前输入：" + furnacePressureInput.text + " Pa"
                settingsErrorDialog.open()
                return
            }

            // 验证设定氧气浓度输入
            var concentration = parseFloat(oxygenConcentrationInput.text)
            if (isNaN(concentration) || concentration < 0.0 || concentration > 21.0) {
                settingsErrorDialog.errorMessage = "设定氧气浓度超出范围！\n有效范围：0 ~ 21 %\n当前输入：" + oxygenConcentrationInput.text + " %"
                settingsErrorDialog.open()
                return
            }

            // 验证临时炉膛设定温度输入
            var tempFurnaceTemp = parseFloat(tempFurnaceSetTempInput.text)
            if (isNaN(tempFurnaceTemp) || tempFurnaceTemp < 800.0 || tempFurnaceTemp > 1200.0) {
                settingsErrorDialog.errorMessage = "临时炉膛设定温度超出范围！\n有效范围：800 ~ 1200 ℃\n当前输入：" + tempFurnaceSetTempInput.text + " ℃"
                settingsErrorDialog.open()
                return
            }

            // 验证临时计划生料量输入
            var tempPlannedMaterial = parseFloat(tempPlannedRawMaterialInput.text)
            if (isNaN(tempPlannedMaterial) || tempPlannedMaterial < 0.0) {
                settingsErrorDialog.errorMessage = "临时计划生料量不能为负数！\n当前输入：" + tempPlannedRawMaterialInput.text + " kg"
                settingsErrorDialog.open()
                return
            }

            // 保存给定炉压
            parameterAdjustmentQML.setFurnacePressureSetpoint(pressure)

            // 保存设定氧气浓度
            parameterAdjustmentQML.setOxygenConcentrationSetpoint(concentration)

            // 保存临时炉膛设定温度
            parameterAdjustmentQML.setTempFurnaceSetTemp(tempFurnaceTemp)

            // 保存临时计划生料量
            parameterAdjustmentQML.setTempPlannedRawMaterial(tempPlannedMaterial)

            // 保存生料量设定目标选择
            parameterAdjustmentQML.setRawMaterialSetpointTarget(rawMaterialSetpointTarget)

            // 显示成功提示
            settingsSuccessDialog.open()

        } catch (error) {
            settingsErrorDialog.errorMessage = "保存设置时发生错误：\n" + error.toString()
            settingsErrorDialog.open()
        }
    }

    // 自动调整确认对话框
    Dialog {
        id: autoAdjustmentDialog
        title: "自动调整确认"
        anchors.centerIn: parent
        width: 400
        height: 200
        modal: true

        background: Rectangle {
            color: "#ffffff"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 2
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "启用自动调整模式后，系统将持续监控参数"
                font.pixelSize: 16
                color: "#333333"
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
                horizontalAlignment: Text.AlignHCenter
            }

            Label {
                text: "并自动向DCS发送调整指令，请确认是否启用？"
                font.pixelSize: 14
                font.bold: true
                color: "#d32f2f"
                Layout.fillWidth: true
                horizontalAlignment: Text.AlignHCenter
            }

            RowLayout {
                Layout.alignment: Qt.AlignHCenter
                spacing: 20

                Button {
                    text: "确认"
                    Layout.preferredWidth: 100
                    Layout.preferredHeight: 35
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 6
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    onClicked: {
                        autoAdjustmentDialog.close()
                        executeAutoAdjustment()
                    }
                }

                Button {
                    text: "取消"
                    Layout.preferredWidth: 100
                    Layout.preferredHeight: 35
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 6
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    onClicked: {
                        autoAdjustmentDialog.close()
                    }
                }
            }
        }
    }

    // 手动调整确认对话框
    Dialog {
        id: manualAdjustmentDialog
        title: "手动调整提示"
        anchors.centerIn: parent
        width: 400
        height: 200
        modal: true

        background: Rectangle {
            color: "#ffffff"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 2
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "系统只提供调整建议"
                font.pixelSize: 16
                color: "#333333"
                Layout.fillWidth: true
                horizontalAlignment: Text.AlignHCenter
            }

            Label {
                text: "需要用户手动在DCS系统中进行调整"
                font.pixelSize: 14
                font.bold: true
                color: "#1976d2"
                Layout.fillWidth: true
                horizontalAlignment: Text.AlignHCenter
            }

            Button {
                text: "确定"
                Layout.alignment: Qt.AlignHCenter
                Layout.preferredWidth: 100
                Layout.preferredHeight: 35
                background: Rectangle {
                    color: parent.pressed ? "#1976d2" : "#2196f3"
                    radius: 6
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: {
                    manualAdjustmentDialog.close()
                    executeManualAdjustment()
                }
            }
        }
    }

    // 执行自动调整
    function executeAutoAdjustment() {
        // 启用自动调整模式
        parameterAdjustmentQML.toggleAutoAdjustment(true)

        // 更新界面状态
        isAutoMode = true

        // 立即更新参数显示（包含最新的调整建议）
        updateParameterData()
    }

    // 执行手动调整
    function executeManualAdjustment() {
        // 禁用自动调整模式（手动模式）
        parameterAdjustmentQML.toggleAutoAdjustment(false)

        // 更新界面状态
        isAutoMode = false

        // 立即更新参数显示（包含最新的调整建议）
        updateParameterData()
    }



    // 设置保存成功对话框
    Dialog {
        id: settingsSuccessDialog
        title: "保存成功"
        anchors.centerIn: parent
        width: 300
        height: 150
        modal: true

        background: Rectangle {
            color: "#ffffff"
            radius: 10
            border.color: "#4caf50"
            border.width: 2
        }

        Column {
            anchors.centerIn: parent
            spacing: 20

            Label {
                text: "✓ 参数设置已成功保存！"
                font.pixelSize: 16
                font.bold: true
                color: "#4caf50"
                anchors.horizontalCenter: parent.horizontalCenter
            }

            Button {
                text: "确定"
                anchors.horizontalCenter: parent.horizontalCenter
                width: 80
                height: 35
                background: Rectangle {
                    color: parent.pressed ? "#388e3c" : "#4caf50"
                    radius: 6
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: settingsSuccessDialog.close()
            }
        }
    }

    // 设置保存失败对话框
    Dialog {
        id: settingsErrorDialog
        title: "保存失败"
        anchors.centerIn: parent
        width: 350
        height: 200
        modal: true

        property string errorMessage: ""

        background: Rectangle {
            color: "#ffffff"
            radius: 10
            border.color: "#f44336"
            border.width: 2
        }

        Column {
            anchors.centerIn: parent
            spacing: 20

            Label {
                text: "✗ " + settingsErrorDialog.errorMessage
                font.pixelSize: 14
                color: "#f44336"
                wrapMode: Text.WordWrap
                width: 300
                anchors.horizontalCenter: parent.horizontalCenter
                horizontalAlignment: Text.AlignHCenter
            }

            Button {
                text: "确定"
                anchors.horizontalCenter: parent.horizontalCenter
                width: 80
                height: 35
                background: Rectangle {
                    color: parent.pressed ? "#d32f2f" : "#f44336"
                    radius: 6
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: settingsErrorDialog.close()
            }
        }
    }
}